#!/usr/bin/env python3
"""
批量修复Flutter项目中的Text组件错误
"""

import os
import re
import subprocess
import sys

def fix_text_style_errors(content):
    """修复Text组件中的样式错误"""
    changes = []
    
    # 1. 修复单个fontSize参数
    pattern1 = r'Text\s*\(\s*([^,\)]+),\s*fontSize:\s*([^,\)]+)\s*\)'
    if re.search(pattern1, content):
        content = re.sub(pattern1, r'Text(\1, style: TextStyle(fontSize: \2))', content)
        changes.append("Fixed fontSize-only errors")
    
    # 2. 修复单个color参数
    pattern2 = r'Text\s*\(\s*([^,\)]+),\s*color:\s*([^,\)]+)\s*\)'
    if re.search(pattern2, content):
        content = re.sub(pattern2, r'Text(\1, style: TextStyle(color: \2))', content)
        changes.append("Fixed color-only errors")
    
    # 3. 修复单个fontWeight参数
    pattern3 = r'Text\s*\(\s*([^,\)]+),\s*fontWeight:\s*([^,\)]+)\s*\)'
    if re.search(pattern3, content):
        content = re.sub(pattern3, r'Text(\1, style: TextStyle(fontWeight: \2))', content)
        changes.append("Fixed fontWeight-only errors")
    
    # 4. 修复fontSize + color组合
    pattern4 = r'Text\s*\(\s*([^,\)]+),\s*fontSize:\s*([^,]+),\s*color:\s*([^,\)]+)\s*\)'
    if re.search(pattern4, content):
        content = re.sub(pattern4, r'Text(\1, style: TextStyle(fontSize: \2, color: \3))', content)
        changes.append("Fixed fontSize+color errors")
    
    # 5. 修复fontSize + fontWeight组合
    pattern5 = r'Text\s*\(\s*([^,\)]+),\s*fontSize:\s*([^,]+),\s*fontWeight:\s*([^,\)]+)\s*\)'
    if re.search(pattern5, content):
        content = re.sub(pattern5, r'Text(\1, style: TextStyle(fontSize: \2, fontWeight: \3))', content)
        changes.append("Fixed fontSize+fontWeight errors")
    
    # 6. 修复color + fontWeight组合
    pattern6 = r'Text\s*\(\s*([^,\)]+),\s*color:\s*([^,]+),\s*fontWeight:\s*([^,\)]+)\s*\)'
    if re.search(pattern6, content):
        content = re.sub(pattern6, r'Text(\1, style: TextStyle(color: \2, fontWeight: \3))', content)
        changes.append("Fixed color+fontWeight errors")
    
    # 7. 修复三个参数的组合
    pattern7 = r'Text\s*\(\s*([^,\)]+),\s*fontSize:\s*([^,]+),\s*color:\s*([^,]+),\s*fontWeight:\s*([^,\)]+)\s*\)'
    if re.search(pattern7, content):
        content = re.sub(pattern7, r'Text(\1, style: TextStyle(fontSize: \2, color: \3, fontWeight: \4))', content)
        changes.append("Fixed fontSize+color+fontWeight errors")
    
    return content, changes

def fix_textstyle_colorscheme_errors(content):
    """修复TextStyle中错误的colorScheme链式调用"""
    changes = []
    
    # 修复 TextStyle(...).colorScheme.xxx 模式
    pattern = r'TextStyle\s*\([^)]*color:\s*Theme\.of\([^)]+\)\s*\)\s*\.colorScheme\s*\.\s*(\w+)'
    matches = re.findall(pattern, content)
    if matches:
        # 替换为正确的格式
        content = re.sub(
            r'TextStyle\s*\(([^)]*color:\s*)Theme\.of\(([^)]+)\)\s*\)\s*\.colorScheme\s*\.\s*(\w+)',
            r'TextStyle(\1Theme.of(\2).colorScheme.\3)',
            content
        )
        changes.append(f"Fixed {len(matches)} TextStyle colorScheme chain errors")
    
    return content, changes

def fix_file(file_path):
    """修复单个文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        all_changes = []
        
        # 修复Text组件样式错误
        content, changes1 = fix_text_style_errors(content)
        all_changes.extend(changes1)
        
        # 修复TextStyle colorScheme错误
        content, changes2 = fix_textstyle_colorscheme_errors(content)
        all_changes.extend(changes2)
        
        # 如果有修改，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Fixed {file_path}: {', '.join(all_changes)}")
            return True
        else:
            return False
            
    except Exception as e:
        print(f"❌ Error fixing {file_path}: {e}")
        return False

def find_dart_files():
    """查找所有Dart文件"""
    dart_files = []
    for root, dirs, files in os.walk('lib'):
        for file in files:
            if file.endswith('.dart'):
                dart_files.append(os.path.join(root, file))
    return dart_files

def main():
    """主函数"""
    print("🚀 开始批量修复Text组件错误...")
    
    dart_files = find_dart_files()
    print(f"📁 找到 {len(dart_files)} 个Dart文件")
    
    fixed_count = 0
    for file_path in dart_files:
        if fix_file(file_path):
            fixed_count += 1
    
    print(f"\n✨ 修复完成！共修复了 {fixed_count} 个文件")
    
    # 运行flutter analyze检查结果
    print("\n🔍 运行flutter analyze检查修复结果...")
    try:
        result = subprocess.run(['flutter', 'analyze'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 所有错误已修复！")
        else:
            print("⚠️  仍有一些问题需要手动修复:")
            print(result.stdout)
    except Exception as e:
        print(f"❌ 无法运行flutter analyze: {e}")

if __name__ == "__main__":
    main()
