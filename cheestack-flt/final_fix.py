#!/usr/bin/env python3
"""
最终批量修复脚本
"""

import os
import re
import subprocess

def fix_all_text_errors(file_path):
    """修复所有Text组件错误"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 1. 修复所有Text组件的fontSize、color、fontWeight参数
        # 匹配各种Text组件错误模式
        patterns = [
            # 单个参数
            (r'Text\s*\(\s*([^,\)]+),\s*fontSize:\s*([^,\)]+)\s*\)', r'Text(\1, style: TextStyle(fontSize: \2))'),
            (r'Text\s*\(\s*([^,\)]+),\s*color:\s*([^,\)]+)\s*\)', r'Text(\1, style: TextStyle(color: \2))'),
            (r'Text\s*\(\s*([^,\)]+),\s*fontWeight:\s*([^,\)]+)\s*\)', r'Text(\1, style: TextStyle(fontWeight: \2))'),
            
            # 两个参数组合
            (r'Text\s*\(\s*([^,\)]+),\s*fontSize:\s*([^,]+),\s*color:\s*([^,\)]+)\s*\)', 
             r'Text(\1, style: TextStyle(fontSize: \2, color: \3))'),
            (r'Text\s*\(\s*([^,\)]+),\s*color:\s*([^,]+),\s*fontSize:\s*([^,\)]+)\s*\)', 
             r'Text(\1, style: TextStyle(color: \2, fontSize: \3))'),
            (r'Text\s*\(\s*([^,\)]+),\s*fontSize:\s*([^,]+),\s*fontWeight:\s*([^,\)]+)\s*\)', 
             r'Text(\1, style: TextStyle(fontSize: \2, fontWeight: \3))'),
            (r'Text\s*\(\s*([^,\)]+),\s*fontWeight:\s*([^,]+),\s*fontSize:\s*([^,\)]+)\s*\)', 
             r'Text(\1, style: TextStyle(fontWeight: \2, fontSize: \3))'),
            (r'Text\s*\(\s*([^,\)]+),\s*color:\s*([^,]+),\s*fontWeight:\s*([^,\)]+)\s*\)', 
             r'Text(\1, style: TextStyle(color: \2, fontWeight: \3))'),
            (r'Text\s*\(\s*([^,\)]+),\s*fontWeight:\s*([^,]+),\s*color:\s*([^,\)]+)\s*\)', 
             r'Text(\1, style: TextStyle(fontWeight: \2, color: \3))'),
            
            # 三个参数组合
            (r'Text\s*\(\s*([^,\)]+),\s*fontSize:\s*([^,]+),\s*color:\s*([^,]+),\s*fontWeight:\s*([^,\)]+)\s*\)', 
             r'Text(\1, style: TextStyle(fontSize: \2, color: \3, fontWeight: \4))'),
            (r'Text\s*\(\s*([^,\)]+),\s*fontSize:\s*([^,]+),\s*fontWeight:\s*([^,]+),\s*color:\s*([^,\)]+)\s*\)', 
             r'Text(\1, style: TextStyle(fontSize: \2, fontWeight: \3, color: \4))'),
            (r'Text\s*\(\s*([^,\)]+),\s*color:\s*([^,]+),\s*fontSize:\s*([^,]+),\s*fontWeight:\s*([^,\)]+)\s*\)', 
             r'Text(\1, style: TextStyle(color: \2, fontSize: \3, fontWeight: \4))'),
            (r'Text\s*\(\s*([^,\)]+),\s*color:\s*([^,]+),\s*fontWeight:\s*([^,]+),\s*fontSize:\s*([^,\)]+)\s*\)', 
             r'Text(\1, style: TextStyle(color: \2, fontWeight: \3, fontSize: \4))'),
            (r'Text\s*\(\s*([^,\)]+),\s*fontWeight:\s*([^,]+),\s*fontSize:\s*([^,]+),\s*color:\s*([^,\)]+)\s*\)', 
             r'Text(\1, style: TextStyle(fontWeight: \2, fontSize: \3, color: \4))'),
            (r'Text\s*\(\s*([^,\)]+),\s*fontWeight:\s*([^,]+),\s*color:\s*([^,]+),\s*fontSize:\s*([^,\)]+)\s*\)', 
             r'Text(\1, style: TextStyle(fontWeight: \2, color: \3, fontSize: \4))'),
        ]
        
        for pattern, replacement in patterns:
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
        
        # 2. 修复TextStyle中错误的colorScheme链式调用
        # 修复 TextStyle(...).colorScheme.xxx 模式
        content = re.sub(
            r'TextStyle\s*\([^)]*color:\s*Theme\.of\([^)]+\)\s*\)\s*\.colorScheme\s*\.\s*(\w+)',
            lambda m: m.group(0).replace(').colorScheme.', '.colorScheme.'),
            content
        )
        
        # 3. 修复空安全错误
        # String? -> String
        content = re.sub(r'Text\(([^,\)]+)\)', lambda m: f'Text({m.group(1)} ?? "")' if '?' in m.group(1) else m.group(0), content)
        
        # 4. 移除未使用的导入
        unused_imports = [
            r"import 'package:cheestack_flt/widgets/index\.dart';\n",
            r"import 'package:cheestack_flt/theme\.dart';\n",
            r"import 'package:cheestack_flt/services/index\.dart';\n",
        ]
        
        for pattern in unused_imports:
            content = re.sub(pattern, '', content)
        
        # 5. 修复已弃用的API
        content = re.sub(r'\.withOpacity\(([^)]+)\)', r'.withValues(alpha: \1)', content)
        content = re.sub(r'onHttpClientCreate', 'createHttpClient', content)
        
        # 如果有修改，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"Error fixing {file_path}: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始最终批量修复...")
    
    # 查找所有Dart文件
    dart_files = []
    for root, dirs, files in os.walk('lib'):
        for file in files:
            if file.endswith('.dart'):
                dart_files.append(os.path.join(root, file))
    
    print(f"📁 找到 {len(dart_files)} 个Dart文件")
    
    fixed_count = 0
    for file_path in dart_files:
        if fix_all_text_errors(file_path):
            fixed_count += 1
            print(f"✅ Fixed {file_path}")
    
    print(f"\n✨ 修复完成！共修复了 {fixed_count} 个文件")
    
    # 运行flutter analyze检查结果
    print("\n🔍 运行flutter analyze检查修复结果...")
    try:
        result = subprocess.run(['flutter', 'analyze'], capture_output=True, text=True)
        error_count = result.stdout.count('error •')
        warning_count = result.stdout.count('warning •')
        
        print(f"📊 剩余错误: {error_count}, 警告: {warning_count}")
        
        if error_count == 0:
            print("✅ 所有错误已修复！")
        else:
            print("⚠️  仍有一些错误需要手动修复")
            
    except Exception as e:
        print(f"❌ 无法运行flutter analyze: {e}")

if __name__ == "__main__":
    main()
