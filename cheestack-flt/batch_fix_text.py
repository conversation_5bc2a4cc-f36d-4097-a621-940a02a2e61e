#!/usr/bin/env python3
"""
批量修复Flutter项目中Text组件的使用错误
将直接的fontSize、color、fontWeight等参数转换为TextStyle
"""

import os
import re
import sys
from pathlib import Path


def fix_text_errors_in_file(file_path):
    """修复单个文件中的Text组件错误"""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        original_content = content
        changes_made = []

        # 模式1: 修复单个fontSize参数
        pattern1 = r"Text\s*\(\s*([^,\)]+),\s*fontSize:\s*([^,\)]+)\s*\)"
        matches1 = re.findall(pattern1, content)
        if matches1:
            content = re.sub(pattern1, r"Text(\1, style: TextStyle(fontSize: \2))", content)
            changes_made.append(f"Fixed {len(matches1)} fontSize-only errors")

        # 模式2: 修复单个color参数
        pattern2 = r"Text\s*\(\s*([^,\)]+),\s*color:\s*([^,\)]+)\s*\)"
        matches2 = re.findall(pattern2, content)
        if matches2:
            content = re.sub(pattern2, r"Text(\1, style: TextStyle(color: \2))", content)
            changes_made.append(f"Fixed {len(matches2)} color-only errors")

        # 模式3: 修复单个fontWeight参数
        pattern3 = r"Text\s*\(\s*([^,\)]+),\s*fontWeight:\s*([^,\)]+)\s*\)"
        matches3 = re.findall(pattern3, content)
        if matches3:
            content = re.sub(pattern3, r"Text(\1, style: TextStyle(fontWeight: \2))", content)
            changes_made.append(f"Fixed {len(matches3)} fontWeight-only errors")

        # 模式4: 修复fontSize + color组合
        pattern4 = r"Text\s*\(\s*([^,\)]+),\s*fontSize:\s*([^,]+),\s*color:\s*([^,\)]+)\s*\)"
        matches4 = re.findall(pattern4, content)
        if matches4:
            content = re.sub(pattern4, r"Text(\1, style: TextStyle(fontSize: \2, color: \3))", content)
            changes_made.append(f"Fixed {len(matches4)} fontSize+color errors")

        # 模式5: 修复fontSize + fontWeight组合
        pattern5 = r"Text\s*\(\s*([^,\)]+),\s*fontSize:\s*([^,]+),\s*fontWeight:\s*([^,\)]+)\s*\)"
        matches5 = re.findall(pattern5, content)
        if matches5:
            content = re.sub(pattern5, r"Text(\1, style: TextStyle(fontSize: \2, fontWeight: \3))", content)
            changes_made.append(f"Fixed {len(matches5)} fontSize+fontWeight errors")

        # 模式6: 修复color + fontWeight组合
        pattern6 = r"Text\s*\(\s*([^,\)]+),\s*color:\s*([^,]+),\s*fontWeight:\s*([^,\)]+)\s*\)"
        matches6 = re.findall(pattern6, content)
        if matches6:
            content = re.sub(pattern6, r"Text(\1, style: TextStyle(color: \2, fontWeight: \3))", content)
            changes_made.append(f"Fixed {len(matches6)} color+fontWeight errors")

        # 模式7: 修复三个参数的组合
        pattern7 = r"Text\s*\(\s*([^,\)]+),\s*fontSize:\s*([^,]+),\s*color:\s*([^,]+),\s*fontWeight:\s*([^,\)]+)\s*\)"
        matches7 = re.findall(pattern7, content)
        if matches7:
            content = re.sub(
                pattern7, r"Text(\1, style: TextStyle(fontSize: \2, color: \3, fontWeight: \4))", content
            )
            changes_made.append(f"Fixed {len(matches7)} fontSize+color+fontWeight errors")

        # 模式8: 修复带其他参数的复杂情况
        # 修复带textAlign的情况
        pattern8 = r"Text\s*\(\s*([^,\)]+),\s*fontSize:\s*([^,]+),\s*textAlign:\s*([^,\)]+)\s*\)"
        matches8 = re.findall(pattern8, content)
        if matches8:
            content = re.sub(pattern8, r"Text(\1, style: TextStyle(fontSize: \2), textAlign: \3)", content)
            changes_made.append(f"Fixed {len(matches8)} fontSize+textAlign errors")

        # 模式9: 修复更复杂的多行情况
        # 处理跨行的Text组件错误
        multiline_pattern = (
            r"Text\s*\(\s*([^,\)]+),\s*\n\s*fontSize:\s*([^,\)]+)\s*(?:,\s*\n\s*([^)]+))?\s*\)"
        )
        multiline_matches = re.findall(multiline_pattern, content, re.MULTILINE)
        if multiline_matches:
            for match in multiline_matches:
                text_content = match[0]
                font_size = match[1]
                other_params = match[2] if match[2] else ""

                old_pattern = (
                    f"Text\\s*\\(\\s*{re.escape(text_content)},\\s*\\n\\s*fontSize:\\s*{re.escape(font_size)}"
                )
                if other_params:
                    old_pattern += f"\\s*,\\s*\\n\\s*{re.escape(other_params)}"
                old_pattern += "\\s*\\)"

                if other_params:
                    replacement = (
                        f"Text({text_content}, style: TextStyle(fontSize: {font_size}), {other_params})"
                    )
                else:
                    replacement = f"Text({text_content}, style: TextStyle(fontSize: {font_size}))"

                content = re.sub(old_pattern, replacement, content, flags=re.MULTILINE)
            changes_made.append(f"Fixed {len(multiline_matches)} multiline errors")

        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            print(f"✅ Fixed {file_path}")
            for change in changes_made:
                print(f"   - {change}")
            return True

        return False

    except Exception as e:
        print(f"❌ Error processing {file_path}: {e}")
        return False


def find_dart_files(directory):
    """递归查找所有.dart文件"""
    dart_files = []
    for root, dirs, files in os.walk(directory):
        # 跳过不需要的目录
        dirs[:] = [
            d
            for d in dirs
            if d
            not in [
                "build",
                ".dart_tool",
                "ios",
                "android",
                "web",
                "windows",
                "linux",
                "macos",
                ".git",
                "node_modules",
            ]
        ]

        for file in files:
            if file.endswith(".dart"):
                dart_files.append(os.path.join(root, file))

    return dart_files


def main():
    """主函数"""
    project_dir = "lib"  # 只处理lib目录

    print("🔍 扫描Dart文件...")
    dart_files = find_dart_files(project_dir)
    print(f"📁 找到 {len(dart_files)} 个Dart文件")

    print("\n🔧 开始批量修复Text组件错误...")
    fixed_count = 0

    for file_path in sorted(dart_files):
        if fix_text_errors_in_file(file_path):
            fixed_count += 1

    print(f"\n✨ 修复完成！共修复了 {fixed_count} 个文件")
    print("\n🧪 运行 'flutter analyze' 来验证修复结果")


if __name__ == "__main__":
    main()
