#!/usr/bin/env python3
"""
批量修复剩余的Flutter错误
"""

import os
import re
import subprocess
import sys


def fix_remaining_text_errors(content):
    """修复剩余的Text组件错误"""
    changes = []

    # 修复更复杂的Text组件错误模式
    patterns = [
        # 修复带有多行的fontSize错误
        (r"Text\s*\(\s*([^,\)]+),\s*\n\s*fontSize:\s*([^,\n]+)", r"Text(\1, style: TextStyle(fontSize: \2)"),
        # 修复带有多行的color错误
        (r"Text\s*\(\s*([^,\)]+),\s*\n\s*color:\s*([^,\n]+)", r"Text(\1, style: TextStyle(color: \2)"),
        # 修复带有多行的fontWeight错误
        (
            r"Text\s*\(\s*([^,\)]+),\s*\n\s*fontWeight:\s*([^,\n]+)",
            r"Text(\1, style: TextStyle(fontWeight: \2)",
        ),
        # 修复多行fontSize + color组合
        (
            r"Text\s*\(\s*([^,\)]+),\s*\n\s*fontSize:\s*([^,\n]+),\s*\n\s*color:\s*([^,\n]+)",
            r"Text(\1, style: TextStyle(fontSize: \2, color: \3)",
        ),
        # 修复多行color + fontSize组合
        (
            r"Text\s*\(\s*([^,\)]+),\s*\n\s*color:\s*([^,\n]+),\s*\n\s*fontSize:\s*([^,\n]+)",
            r"Text(\1, style: TextStyle(color: \2, fontSize: \3)",
        ),
    ]

    for pattern, replacement in patterns:
        if re.search(pattern, content, re.MULTILINE):
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
            changes.append(f"Fixed pattern: {pattern[:50]}...")

    return content, changes


def fix_unused_imports(content):
    """修复未使用的导入"""
    changes = []

    # 移除未使用的导入
    unused_imports = [
        r"import 'package:cheestack_flt/widgets/index\.dart';\n",
        r"import 'package:cheestack_flt/theme\.dart';\n",
        r"import 'package:cheestack_flt/services/index\.dart';\n",
    ]

    for pattern in unused_imports:
        if re.search(pattern, content):
            content = re.sub(pattern, "", content)
            changes.append(f"Removed unused import")

    return content, changes


def fix_deprecated_apis(content):
    """修复已弃用的API"""
    changes = []

    # 修复withOpacity -> withValues
    if ".withOpacity(" in content:
        content = re.sub(r"\.withOpacity\(([^)]+)\)", r".withValues(alpha: \1)", content)
        changes.append("Fixed withOpacity -> withValues")

    # 修复onHttpClientCreate -> createHttpClient
    if "onHttpClientCreate" in content:
        content = re.sub(r"onHttpClientCreate", "createHttpClient", content)
        changes.append("Fixed onHttpClientCreate -> createHttpClient")

    return content, changes


def fix_undefined_methods(content):
    """修复未定义的方法"""
    changes = []

    # 修复CachedNetworkImage未定义的问题
    if (
        "CachedNetworkImage(" in content
        and "import 'package:cached_network_image/cached_network_image.dart';" not in content
    ):
        # 在import部分添加缺失的导入
        import_pattern = r"(import \'package:flutter/material\.dart\';)"
        if re.search(import_pattern, content):
            content = re.sub(
                import_pattern,
                r"\1\nimport \'package:cached_network_image/cached_network_image.dart\';",
                content,
            )
            changes.append("Added missing CachedNetworkImage import")

    return content, changes


def fix_file(file_path):
    """修复单个文件"""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        original_content = content
        all_changes = []

        # 修复剩余的Text组件错误
        content, changes1 = fix_remaining_text_errors(content)
        all_changes.extend(changes1)

        # 修复未使用的导入
        content, changes2 = fix_unused_imports(content)
        all_changes.extend(changes2)

        # 修复已弃用的API
        content, changes3 = fix_deprecated_apis(content)
        all_changes.extend(changes3)

        # 修复未定义的方法
        content, changes4 = fix_undefined_methods(content)
        all_changes.extend(changes4)

        # 如果有修改，写回文件
        if content != original_content:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            print(f"✅ Fixed {file_path}: {', '.join(all_changes)}")
            return True
        else:
            return False

    except Exception as e:
        print(f"❌ Error fixing {file_path}: {e}")
        return False


def get_error_files():
    """获取有错误的文件列表"""
    try:
        result = subprocess.run(["flutter", "analyze"], capture_output=True, text=True)
        error_files = set()

        lines = result.stdout.split("\n")
        for i, line in enumerate(lines):
            if "error •" in line or "warning •" in line:
                # 查找下一行是否包含文件路径
                if i + 1 < len(lines):
                    next_line = lines[i + 1].strip()
                    if next_line.startswith("lib/") and ":" in next_line:
                        file_path = next_line.split("•")[0].strip()
                        if os.path.exists(file_path):
                            error_files.add(file_path)

                # 也检查当前行是否直接包含文件路径
                if "lib/" in line:
                    parts = line.split("•")
                    for part in parts:
                        part = part.strip()
                        if part.startswith("lib/") and ":" in part:
                            file_path = part.split(":")[0]
                            if os.path.exists(file_path):
                                error_files.add(file_path)

        return list(error_files)
    except Exception as e:
        print(f"Error getting error files: {e}")
        return []


def main():
    """主函数"""
    print("🚀 开始修复剩余错误...")

    error_files = get_error_files()
    print(f"📁 找到 {len(error_files)} 个有错误的文件")

    fixed_count = 0
    for file_path in error_files:
        if fix_file(file_path):
            fixed_count += 1

    print(f"\n✨ 修复完成！共修复了 {fixed_count} 个文件")

    # 再次运行flutter analyze检查结果
    print("\n🔍 运行flutter analyze检查修复结果...")
    try:
        result = subprocess.run(["flutter", "analyze"], capture_output=True, text=True)
        error_count = result.stdout.count("error •")
        warning_count = result.stdout.count("warning •")

        print(f"📊 剩余错误: {error_count}, 警告: {warning_count}")

        if error_count == 0:
            print("✅ 所有错误已修复！")
        else:
            print("⚠️  仍有一些错误需要手动修复")

    except Exception as e:
        print(f"❌ 无法运行flutter analyze: {e}")


if __name__ == "__main__":
    main()
