#!/usr/bin/env python3
"""
修复单个文件中的所有Text组件错误
"""

import re
import sys

def fix_text_errors_in_single_file(file_path):
    """修复单个文件中的所有Text组件错误"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复所有 fontSize + color 组合
        pattern1 = r'Text\s*\(\s*([^,\)]+),\s*fontSize:\s*([^,]+),\s*color:\s*([^,\)]+)\s*\)'
        content = re.sub(pattern1, r'Text(\1, style: TextStyle(fontSize: \2, color: \3))', content)
        
        # 修复单独的 fontSize
        pattern2 = r'Text\s*\(\s*([^,\)]+),\s*fontSize:\s*([^,\)]+)\s*\)'
        content = re.sub(pattern2, r'Text(\1, style: TextStyle(fontSize: \2))', content)
        
        # 修复单独的 color
        pattern3 = r'Text\s*\(\s*([^,\)]+),\s*color:\s*([^,\)]+)\s*\)'
        content = re.sub(pattern3, r'Text(\1, style: TextStyle(color: \2))', content)
        
        # 修复单独的 fontWeight
        pattern4 = r'Text\s*\(\s*([^,\)]+),\s*fontWeight:\s*([^,\)]+)\s*\)'
        content = re.sub(pattern4, r'Text(\1, style: TextStyle(fontWeight: \2))', content)
        
        # 修复 fontSize + fontWeight 组合
        pattern5 = r'Text\s*\(\s*([^,\)]+),\s*fontSize:\s*([^,]+),\s*fontWeight:\s*([^,\)]+)\s*\)'
        content = re.sub(pattern5, r'Text(\1, style: TextStyle(fontSize: \2, fontWeight: \3))', content)
        
        # 修复 color + fontWeight 组合
        pattern6 = r'Text\s*\(\s*([^,\)]+),\s*color:\s*([^,]+),\s*fontWeight:\s*([^,\)]+)\s*\)'
        content = re.sub(pattern6, r'Text(\1, style: TextStyle(color: \2, fontWeight: \3))', content)
        
        # 修复三个参数的组合
        pattern7 = r'Text\s*\(\s*([^,\)]+),\s*fontSize:\s*([^,]+),\s*color:\s*([^,]+),\s*fontWeight:\s*([^,\)]+)\s*\)'
        content = re.sub(pattern7, r'Text(\1, style: TextStyle(fontSize: \2, color: \3, fontWeight: \4))', content)
        
        # 处理多行情况
        # 匹配跨行的Text组件
        multiline_pattern = r'Text\s*\(\s*([^,\)]+),\s*\n\s*fontSize:\s*([^,\n]+),\s*\n\s*color:\s*([^,\n\)]+)\s*\)'
        content = re.sub(multiline_pattern, r'Text(\1, style: TextStyle(fontSize: \2, color: \3))', content, flags=re.MULTILINE)
        
        # 处理const关键字问题
        content = re.sub(r'const Text\(([^,\)]+), style: TextStyle\(', r'Text(\1, style: TextStyle(', content)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Fixed {file_path}")
            return True
        else:
            print(f"ℹ️  No changes needed for {file_path}")
            return False
        
    except Exception as e:
        print(f"❌ Error processing {file_path}: {e}")
        return False

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 fix_single_file.py <file_path>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    fix_text_errors_in_single_file(file_path)

if __name__ == '__main__':
    main()
