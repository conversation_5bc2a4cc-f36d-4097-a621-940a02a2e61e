library review;

import 'dart:async';
import 'dart:math';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:cheestack_flt/apis/index.dart';
import 'package:cheestack_flt/common/enums/index.dart';
import 'package:cheestack_flt/shared/extensions/index.dart';
import 'package:cheestack_flt/common/data/index.dart';
import 'package:cheestack_flt/shared/widgets/chv_audio/index.dart';
import 'package:cheestack_flt/controllers/index.dart';
import 'package:cheestack_flt/controllers/load_state_controller.dart';
import 'package:cheestack_flt/controllers/speech_recognition_state.dart';
import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/pages/review/widgets/markdown.dart';
import 'package:cheestack_flt/routes/index.dart';
import 'package:cheestack_flt/shared/utils/oxhttp/index.dart';
import 'package:cheestack_flt/shared/utils/index.dart';
import 'package:cheestack_flt/shared/utils/error_handler.dart';
import 'package:cheestack_flt/widgets/canvas.dart';
import 'package:cheestack_flt/i18n/index.dart';
import 'package:dio/dio.dart' as mdio;
import 'package:easy_rich_text/easy_rich_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart' hide FormData, MultipartFile;
import 'package:permission_handler/permission_handler.dart';
import 'package:reorderables/reorderables.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:string_similarity/string_similarity.dart';
// #docregion platform_imports

// Import for Android features.
// Import for iOS/macOS features.
// #enddocregion platform_imports
import 'package:stroke_order_animator/stroke_order_animator.dart';

part 'controller.dart';
part 'view.dart';
part 'widgets/gen.dart';
part 'widgets/cnw.dart';
part 'widgets/lsn.dart';
part 'widgets/jmt.dart';
part 'widgets/opt.dart';
part 'widgets/say.dart';
part "func.dart";
part 'widgets/hanzi_writer.dart';
part 'widgets/zh_reading.dart';
part 'widgets/lsnopt.dart';
part 'widgets/read_aloud.dart';
