part of review;

class ReviewPage extends StatefulWidget {
  const ReviewPage({super.key});
  @override
  State<ReviewPage> createState() => _ReviewPageState();
}

class _ReviewPageState extends State<ReviewPage> {
  late ReviewController controller;
  late OxAudioController justAudioController;

  @override
  void initState() {
    super.initState();
    // 隐藏状态栏
    // SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);

    if (!Get.isRegistered<ReviewController>()) {
      Console.log("startTime-init");
      Get.put<ReviewController>(ReviewController());
    }
    controller = Get.find<ReviewController>();
  }

  // 在页面销毁时恢复状态栏
  @override
  void dispose() {
    // 恢复状态栏
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ReviewController>(
      init: ReviewController(),
      builder: (_) {
        // 隐藏状态栏
        // SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
        return PopScope(
          onPopInvokedWithResult: (isAlow, result) async {
            // await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
          },
          child: Scaffold(
            appBar: AppBar(
              title: Text("剩余: ${controller.cardModelList.length}"),
              actions: [
                Sbutton.icon(
                  icon: const Icon(Icons.feedback),
                  backgroundColor: const Color.fromARGB(0, 12, 5, 5),
                  onPressed: controller.toFeedback,
                ),
              ],
            ),
            body: SloadStateWrapper(
              state: controller.loadState,
              isShowAd: false,
              isAdCompleted: controller.isAdCompleted,
              onAdStart: controller.onAdStart,
              onAdEnd: controller.onAdFinish,
              child: _buildView(),
            ),
          ),
        );
      },
    );
  }

  /// 主视图
  Widget _buildView() {
    return SafeArea(
        child: Column(
      children: [
        Expanded(child: _buildBody()),
        // const BannerView(),
      ],
    ));
  }

  _buildBody() {
    return GetBuilder<OxAudioController>(
        init: OxAudioController(),
        builder: (audioController) {
          Console.log(controller.curCard.type?.cardType);
          switch (controller.curCard.type?.cardType) {
            case CardType.languageGeneral:
              return _buildLanguageGeneral();
            case CardType.hanziWriter:
              return _buildHanZiSpeechAndWrite();
            case CardType.languageListening:
              return _buildLanguageListening();
            case CardType.generalMarkdown:
              return _buildGeneralMd();
            case CardType.readAloud:
              return _buildReadAloud();
            default:
              return _buildGeneral();
          }
        });
  }

  Widget _buildLanguageGeneral() {
    return GetBuilder<ReviewController>(
        init: ReviewController(),
        builder: (controller) {
          late Widget view;
          switch (controller.pageIndex) {
            case 0:
              view = const OptionView();
              break;
            case 1:
              view = ListeningView(
                sentence: controller.curCard.question ?? "",
                onSubmit: controller.toPageIndex,
              );
              break;
            case 2:
              view = const SayView();
              break;
            default:
              Console.log(controller.pageIndex);

              view = const Center(
                child: Text('已经结束'),
              );
          }
          return view;
        });
  }

  Widget _buildLanguageListening() {
    return Container();
  }

  Widget _buildGeneral() {
    return const General();
  }

  _buildHanZiSpeechAndWrite() {
    return GetBuilder<ReviewController>(
        init: ReviewController(),
        builder: (controller) {
          late Widget view;
          switch (controller.pageIndex) {
            case 0:
              view = const ChineseReadingView();
              // view = _buildHanzWriter();
              break;
            case 1:
              view = _buildHanzWriter();
              break;

            default:
              view = Center(
                child: Text(LocaleKeys.reviewFinished.tr),
              );
          }
          return view;
        });
  }

  _buildHanzWriter() {
    String word = controller.curCard.title ?? "";
    // 如果文字数量大于1或者不是中文, 返回错误界面
    if (word.length > 1 || !word.isChinese) {
      return Center(
        child: Text(LocaleKeys.reviewIncorrectText.tr),
      );
    }
    return HanziWriter(
      word: word,
      onNext: controller.toPageIndex,
    );
  }

  Widget _buildGeneralMd() {
    return Column(
      children: [
        SmoothPageIndicator(
          controller: controller.pageController,
          count: 2,
        ),
        Expanded(
            child: PageView(
          controller: controller.pageController,
          onPageChanged: controller.onPageChanged,
          children: const [
            MarkDownView(pageIndex: 0),
            MarkDownView(pageIndex: 1),
          ],
        ))
      ],
    );
  }

  _buildReadAloud() {
    return Center(
      child: ReadAloudView(),
    );
  }
}
