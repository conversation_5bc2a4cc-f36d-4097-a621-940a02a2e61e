/// 说明
/// 朗读模式
/// 一个播放按钮, 点击后变成停止按钮
/// 居中显示
part of review;

class ReadAloudView extends StatefulWidget {
  const ReadAloudView({
    Key? key,
  }) : super(key: key);

  @override
  ReadAloudViewState createState() => ReadAloudViewState();
}

class ReadAloudViewState extends State<ReadAloudView> {
  // 控制器
  late ReviewController controller;
  late SherpaOnnxController sherpaController;

  // 本地状态
  bool isAudioPlaying = false; // 本地音频播放状态，防止快速点击作弊
  String? _currentCardId; // 用于检测卡片变化
  bool _isInitialStartDone = false; // 标记是否已完成初始启动

  @override
  void initState() {
    super.initState();

    try {
      // 初始化ReviewController
      if (!Get.isRegistered<ReviewController>()) {
        Get.lazyPut<ReviewController>(() => ReviewController());
      }
      controller = Get.find<ReviewController>();

      // 初始化SherpaOnnxController
      if (!Get.isRegistered<SherpaOnnxController>()) {
        Get.lazyPut<SherpaOnnxController>(() => SherpaOnnxController());
      }
      sherpaController = Get.find<SherpaOnnxController>();

      // 设置初始卡片ID
      _currentCardId = controller.curCard.id?.toString();

      // 页面初始化完成后自动开始语音识别
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && !_isInitialStartDone) {
          _isInitialStartDone = true;
          _startSpeechRecognition();
        }
      });
    } catch (e) {
      Console.log("初始化失败: $e");
    }
  }

  @override
  void deactivate() {
    // 页面失活时立即停止语音识别
    try {
      Console.log("ReadAloudView页面失活，停止语音识别...");
      if (Get.isRegistered<SherpaOnnxController>()) {
        sherpaController.forceClearSpeechState();
      }
    } catch (e) {
      Console.log("页面失活时停止语音识别失败: $e");
    }
    super.deactivate();
  }

  @override
  void dispose() {
    try {
      Console.log("开始清理ReadAloudView资源...");

      // 立即停止语音识别（异步但不等待，确保快速清理）
      if (Get.isRegistered<SherpaOnnxController>()) {
        sherpaController.stopSpeechRecognition().catchError((error) {
          Console.log("停止语音识别时出错: $error");
        });
        // 额外保险：直接清理状态
        sherpaController.forceClearSpeechState();
      }

      // 停止音频播放
      if (Get.isRegistered<OxAudioController>()) {
        OxAudioController.to.stop();
      }

      // 清理本地状态
      isAudioPlaying = false;
      _currentCardId = null;
      _isInitialStartDone = false;

      Console.log("ReadAloudView资源清理完成");
    } catch (e) {
      Console.log("dispose 清理资源失败: $e");
    }

    super.dispose();
  }

  // 启动语音识别
  Future<void> _startSpeechRecognition() async {
    if (!mounted) return;

    try {
      final questionText = controller.curCard.question ?? "";
      if (questionText.isEmpty) {
        Console.log("警告: 问题文本为空");
        return;
      }

      await sherpaController.startSpeechRecognition(
        questionText,
        onEndpoint: _onSpeechEndpoint,
        onAutoSubmit: _onAutoSubmit,
        onMaxAttempts: _onMaxAttempts,
        onStateChanged: _onSpeechStateChanged,
      );

      Console.log("语音识别启动成功");
    } catch (e) {
      Console.log("启动语音识别失败: $e");
    }
  }

  // 重启语音识别（先停止再启动）
  Future<void> _restartSpeechRecognition() async {
    if (!mounted) return;

    try {
      Console.log("重启语音识别...");

      // 先停止当前的语音识别
      await sherpaController.stopSpeechRecognition();

      // 等待一小段时间确保停止完成
      await Future.delayed(const Duration(milliseconds: 100));

      // 重新启动语音识别
      await _startSpeechRecognition();

      Console.log("语音识别重启完成");
    } catch (e) {
      Console.log("重启语音识别失败: $e");
    }
  }

  // 处理语音终点事件
  void _onSpeechEndpoint(String recognizedText, double similarity) {
    if (!mounted) return;
    Console.log("语音识别结果: $recognizedText, 相似度: $similarity");
    // 触发UI更新
    setState(() {});
  }

  // 处理自动提交
  void _onAutoSubmit(double score) async {
    if (!mounted) return;
    Console.log("自动提交，分数: $score");

    // 播放成功音效
    _playSuccessSound();

    // 自动提交
    await _autoSubmit();
  }

  // 处理达到最大尝试次数
  void _onMaxAttempts() {
    if (!mounted) return;
    Console.log("达到最大尝试次数");
    setState(() {});
  }

  // 处理语音状态变化
  void _onSpeechStateChanged(bool isRecording, bool isProcessing) {
    if (!mounted) return;
    setState(() {});
  }

  // 根据文本长度计算适合的字体大小
  double _calculateFontSize(int length) {
    if (length <= 10) return 22.0;
    if (length <= 30) return AppTheme.fontTitle;
    if (length <= 60) return AppTheme.fontBody;
    return AppTheme.fontBody;
  }

  // 检查是否可以显示提交按钮（只有尝试次数上限时才显示）
  bool _checkResult() {
    return sherpaController.checkMaxAttemptsReached();
  }

  // 重置数据
  void _resetData() {
    setState(() {
      isAudioPlaying = false; // 重置音频播放状态
    });

    // 重置控制器状态
    controller.isCorrect = true; // 重置为默认正确状态

    // 手动提交时需要重新开始语音识别
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && !sherpaController.isRecording) {
        _startSpeechRecognition();
      }
    });
  }

  // 自动提交（当分数达到0.8以上时）
  Future<void> _autoSubmit() async {
    if (!mounted) return;

    try {
      // 判断最终的正确性 - 自动提交时说明分数达标，标记为正确
      bool finalIsCorrect = controller.isCorrect; // 首先检查是否点击了"忘记"

      // 如果没有点击忘记，且分数达标，标记为正确
      if (finalIsCorrect && sherpaController.checkAutoSubmitCondition()) {
        finalIsCorrect = true;
      }

      // 重置语音识别状态，准备下一个内容
      sherpaController.resetSpeechState();

      // 重置本地状态
      if (mounted) {
        setState(() {
          isAudioPlaying = false;
        });
      }

      controller.isCorrect = true;

      // 处理复习结果，切换到下一个卡片
      await controller.onResulHandle(finalIsCorrect);
    } catch (e) {
      Console.log("自动提交失败: $e");
    }
  }

  // 状态指示
  Widget _buildStatusIndicator() {
    return GetBuilder<SherpaOnnxController>(
      builder: (sherpaController) {
        return Container(
          height: 80, // 调整高度以适应内容
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: GetBuilder<OxAudioController>(
            builder: (saudioController) {
              // 优先检查音频播放状态，如果音频正在播放，显示"请仔细聆听"
              if (saudioController.isForceListening || isAudioPlaying) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.volume_up,
                        size: 28,
                        color: AppTheme.primary,
                      ),
                      const SizedBox(height: 6),
                      const Flexible(
                        child: Text(
                          "请仔细聆听",
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.black54,
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                );
              }

              // 如果没有音频播放，则根据语音识别状态显示优雅的动画
              return Center(
                child: Obx(() {
                  final state = sherpaController.recognitionState;

                  if (state == SpeechRecognitionState.initializing) {
                    return SpeechRecognitionIndicator(
                      state: 'initializing',
                      color: Colors.black54, // 使用idle状态的颜色
                      fontSize: 16.0, // 使用idle状态的字体大小
                    );
                  } else if (state == SpeechRecognitionState.recognizing) {
                    return SpeechRecognitionIndicator(
                      state: 'recognizing',
                      realTimeText: sherpaController.realTimeText,
                      color: AppTheme.primary,
                      fontSize: 14.0,
                    );
                  } else {
                    return SpeechRecognitionIndicator(
                      state: 'idle',
                      color: AppTheme.primary, // 使用initializing状态的颜色
                      fontSize: 14.0, // 使用initializing状态的字体大小
                    );
                  }
                }),
              );
            },
          ),
        );
      },
    );
  }

  // 卡片内容
  Widget _buildContent() {
    final questionText = controller.curCard.question ?? "找不到内容";
    final fontSize = _calculateFontSize(questionText.length);

    return Center(
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.w),
        margin: EdgeInsets.symmetric(horizontal: 16.w),
        constraints: BoxConstraints(maxWidth: 500.w),
        decoration: BoxDecoration(
          color:
              Theme.of(Get.context!).colorScheme.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16.w),
          boxShadow: [
            BoxShadow(
              color: Theme.of(Get.context!)
                  .colorScheme
                  .primary
                  .withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
          border: Border.all(
              color: Theme.of(Get.context!)
                  .colorScheme
                  .primary
                  .withValues(alpha: 0.3),
              width: 1.5),
        ),
        child: Center(
          child: EasyRichText(
            questionText,
            defaultStyle: TextStyle(
              fontSize: fontSize,
              height: 1.3,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
            patternList: [
              ...controller.unmatch.map((e) {
                return EasyRichTextPattern(
                  targetString: e,
                  style: TextStyle(
                    color: Colors.red,
                    
                    fontSize: fontSize,
                  ),
                );
              }).toList(),
            ],
          ),
        ),
      ),
    );
  }

  // 卡片信息
  Widget _buildCardInfo() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 图片
        if (controller.curCard.extra?.get('primaryImageUrl') != null)
          Expanded(
              child: Simage(
            url: controller.curCard.extra?.get('primaryImageUrl') ?? "",
            fit: BoxFit.contain,
          )),
        SizedBox(height: 8.w),
        // 内容
        _buildContent(),
        SizedBox(height: 8.w),
      ],
    );
  }

  // 播放成功音效
  Future<void> _playSuccessSound() async {
    try {
      Console.log("开始播放成功音效");

      if (!Get.isRegistered<OxAudioController>()) {
        Get.lazyPut<OxAudioController>(() => OxAudioController());
      }

      OxAudioController.to.play(AssetPath.success);
    } catch (e) {
      Console.log("播放成功音乐失败: $e");
    }
  }

  // 忘记按钮
  Widget _buildForgetButton() {
    return Sbutton(
      size: SbuttonSize.large,
      shape: SbuttonShape.outline,
      backgroundColor: AppTheme.primary,
      onPressed: () async {
        try {
          Console.log("开始处理忘记按钮点击");

          // 1. 暂停语音识别
          if (sherpaController.isRecording) {
            await sherpaController.pauseSpeechRecognition();
            Console.log("语音识别已暂停");
          }

          // 2. 等待一小段时间确保识别完全停止
          await Future.delayed(const Duration(milliseconds: 200));

          // 3. 设置强制聆听状态，防止快速点击
          OxAudioController.to.isForceListening = true;

          // 4. 标记为忘记状态
          controller.isCorrect = false;
          controller.update();

          // 5. 设置本地音频播放状态
          setState(() {
            isAudioPlaying = true;
          });

          Console.log("开始播放音频");

          // 6. 获取音频URL并播放
          final extra = controller.curCard.extra;
          await OxAudioController.to.play(
            extra?.get('primaryAudioUrl'),
            speed: 1.0,
            isForceListening: true, // 设置强制聆听状态
            onStopCallBack: () {
              // 音频播放完成后重新开始语音识别
              if (mounted) {
                Console.log("音频播放完成，重新开始语音识别");
                setState(() {
                  isAudioPlaying = false;
                });
                // 重新开始语音识别
                _startSpeechRecognition();
              }
            },
          );
        } catch (e) {
          Console.log("忘记按钮操作失败: $e");
          // 发生错误时重置状态
          if (mounted) {
            setState(() {
              isAudioPlaying = false;
            });
          }
        }
      },
      child: const Text('忘记'),
    );
  }

  // 底部按钮区域
  Widget _buildBottomActionBar() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 忘记按钮 - 如果没有达到尝试次数上限时显示
        if (!_checkResult()) Expanded(child: _buildForgetButton()),
        // 提交按钮 - 只在达到尝试次数上限时显示
        if (_checkResult())
          Expanded(
            child: Sbutton(
              size: SbuttonSize.large,
              shape: SbuttonShape.outline,
              backgroundColor: Colors.green,
              onPressed: () async {
                // 停止语音识别
                if (sherpaController.isRecording) {
                  await sherpaController.stopSpeechRecognition();
                }

                // 判断最终的正确性
                bool finalIsCorrect = controller.isCorrect;

                // 如果没有点击忘记，则根据朗读结果判断
                if (finalIsCorrect) {
                  // 检查朗读是否成功（相似度 > 0.8）
                  if (sherpaController.checkAutoSubmitCondition()) {
                    finalIsCorrect = true;
                  } else {
                    // 朗读失败，但如果尝试次数已达上限，仍然允许提交但标记为错误
                    finalIsCorrect = false;
                  }
                }

                // 重置当前页面状态
                _resetData();

                // 处理复习结果，切换到下一个卡片
                await controller.onResulHandle(finalIsCorrect);
              },
              child: const Text("提交"),
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ReviewController>(
        init: ReviewController(),
        builder: (controller) {
          // 检测卡片变化，如果卡片ID发生变化，重新启动语音识别
          final currentCardId = controller.curCard.id?.toString();
          if (_currentCardId != currentCardId && _isInitialStartDone) {
            _currentCardId = currentCardId;
            // 延迟启动语音识别，确保UI更新完成
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted && currentCardId != null) {
                Console.log("检测到卡片变化，重新启动语音识别: $currentCardId");
                _restartSpeechRecognition();
              }
            });
          }

          return GetBuilder<OxAudioController>(
              init: OxAudioController(),
              builder: (_) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // 内容区域
                      Expanded(
                          child: Container(
                        margin: const EdgeInsets.all(16.0),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // 卡片信息
                            Expanded(
                                child: Center(
                              child: SingleChildScrollView(
                                child: _buildCardInfo(),
                              ),
                            )),
                            SizedBox(height: 8.w),
                            // 状态指示
                            _buildStatusIndicator(),
                            SizedBox(height: 8.w),
                          ],
                        ),
                      )),
                      // 底部按钮区域
                      _buildBottomActionBar(),
                    ],
                  ),
                );
              });
        });
  }
}
