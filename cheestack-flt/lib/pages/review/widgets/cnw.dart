part of review;

class CnwV1 extends StatefulWidget {
  final Map<String, dynamic> data;

  const CnwV1({
    Key? key,
    required this.data,
  }) : super(key: key);

  @override
  CnwV1State createState() => CnwV1State();
}

class CnwV1State extends State<CnwV1> {
  @override
  Widget build(BuildContext context) {
    String text = widget.data.get('text') ?? "";

    return Container(
      child: LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          return Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: constraints.biggest.width,
                child: CustomPaint(
                  painter: <PERSON><PERSON><PERSON><PERSON>(constraints),
                  child: Center(
                    child: Text(
                      text.lastChars(1),
                      style: TextStyle(
                        fontSize: constraints.biggest.width * 0.7,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
