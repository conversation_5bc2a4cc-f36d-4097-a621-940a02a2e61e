part of review;

class General extends StatefulWidget {
  const General({super.key});

  @override
  State<General> createState() => _GeneralState();
}

class _GeneralState extends State<General> {
  late ReviewController controller;

  @override
  void initState() {
    super.initState();
    if (!Get.isRegistered<ReviewController>()) {
      Get.lazyPut<ReviewController>(() => ReviewController());
    }
    controller = Get.find<ReviewController>();

    /// 等待组件加载完成后播放音频
    WidgetsBinding.instance.addPostFrameCallback((_) {
      OxAudioController.to
          .play(controller.getAssetUrl(CardAssetType.primaryAudio));
    });
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ReviewController>(
        init: ReviewController(),
        builder: (controller) {
          return Column(
            // crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildPageIndicator(),
              const SizedBox(height: 16),
              Expanded(child: _buildPageView()),
            ],
          );
        });
  }

  SmoothPageIndicator _buildPageIndicator() {
    return SmoothPageIndicator(
      controller: controller.pageController,
      count: 2,
    );
  }

  _buildPageView() {
    return GetBuilder<ReviewController>(
        init: ReviewController(),
        builder: (controller) {
          String? question = controller.curCard.question ?? "";
          String? answer = controller.curCard.answer ?? "";
          String? primaryAudio =
              controller.getAssetUrl(CardAssetType.primaryAudio);
          String? secondaryAudio =
              controller.getAssetUrl(CardAssetType.secondaryAudio);
          String? primaryImage =
              controller.getAssetUrl(CardAssetType.primaryImage);
          String? secondaryImage =
              controller.getAssetUrl(CardAssetType.secondaryImage);

          return PageView(
            controller: controller.pageController,
            onPageChanged: controller.onPageChanged,
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (primaryImage.isNotNullOrEmpty) _buildImage(primaryImage),
                  const SizedBox(height: 16),
                  if (question.isNotNullOrEmpty) _buildText(question),
                  const SizedBox(height: 16),
                  if (primaryAudio.isNotNullOrEmpty)
                    _buildAudioButton(primaryAudio),
                  const SizedBox(height: 16),
                  _buildShowAnswer()
                ],
              ),
              Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (secondaryImage.isNotNullOrEmpty)
                    _buildImage(secondaryImage),
                  const SizedBox(height: 16),
                  if (answer.isNotNullOrEmpty) _buildText(answer),
                  const SizedBox(height: 16),
                  if (secondaryAudio.isNotNullOrEmpty)
                    _buildAudioButton(secondaryAudio),
                  const SizedBox(height: 16),
                  _buildResultButton(),
                ],
              )
            ],
          );
        });
  }

  Widget _buildText(String? text) {
    return Center(
      child: AutoSizeText(
        text ?? "",
        maxLines: 2,
        style: TextStyle(fontSize: FontSize.titleXXL),
        minFontSize: FontSize.titleXXL.truncateToDouble(),
        overflowReplacement: Text(text),
      ),
    );
  }

  _buildAudioButton(String? audioUrl) {
    // 等待插件加载完成后播放音乐
    return SplayWithSpeed(
      onPlayPressed: () => OxAudioController.to.play(audioUrl),
      onSpeedPlayPressed: () => OxAudioController.to.play(audioUrl, speed: 0.6),
    );
  }

  Widget _buildImage(String? primaryImage) {
    return Simage(url: primaryImage ?? "", height: 350.h);
  }

  Widget _buildShowAnswer() {
    return Sbutton(
      width: double.infinity,
      shape: SbuttonShape.outline,
      onPressed: controller.toPageIndex,
      child: const Text("查看答案"),
    );
  }

  _buildResultButton() {
    return Row(
      children: [
        Expanded(
            child: Sbutton(
          shape: SbuttonShape.outline,
          onPressed: () => controller.toPageIndex(isCorrect: false),
          child: const Text("忘记"),
        )),
        Expanded(
            child: Sbutton(
          backgroundColor: AppTheme.success,
          shape: SbuttonShape.outline,
          onPressed: () => controller.toPageIndex(isCorrect: true),
          child: const Text("记得"),
        )),
      ],
    );
  }
}
