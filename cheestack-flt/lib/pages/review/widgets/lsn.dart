part of review;

// class WordItem {
//   int index;
//   String word;
//   bool isCorrect = false;
//   bool isSelected = false;

//   WordItem({
//     required this.index,
//     required this.word,
//     this.isCorrect = false,
//     this.isSelected = false,
//   });
// }

class ListeningView extends StatefulWidget {
  final String sentence;
  final Function()? onSubmit;
  const ListeningView({
    super.key,
    required this.sentence,
    this.onSubmit,
  });

  @override
  State<ListeningView> createState() => _ListeningInputViewState();
}

class _ListeningInputViewState extends State<ListeningView> {
  // get getxcontroller
  late ReviewController controller;
  List<String> originalList = [];
  List<String> shuffledList = [];
  List<String> selectedList = [];
  List<Widget> shuffledListWidetList = [];
  List<Widget> selectedListWidget = [];

  bool isChecked = false;
  // 现有的状态变量...
  List<bool> wordSelectedStatus = []; // 新添加:用于跟踪单词是否被选择

  // 添加新的状态控制
  bool _isProcessing = false;
  final ValueNotifier<bool> _canSubmit = ValueNotifier<bool>(false);
  @override
  void didUpdateWidget(ListeningView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.sentence != widget.sentence) {
      _initializeWordLists();
    }
  }

  @override
  void initState() {
    super.initState();
    // 确保所有状态初始化在同一个同步块中完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeController();
      _initializeWordLists();
      _playPrimaryAudio();
      // 强制更新状态
      setState(() {});
    });
  }

  void _initializeController() {
    if (!Get.isRegistered<ReviewController>()) {
      Get.lazyPut<ReviewController>(() => ReviewController());
    }
    controller = Get.find<ReviewController>();
  }

  void _initializeWordLists() {
    // 添加日志记录原始句子
    originalList = widget.sentence
        .replacePunctuationWithSpace()
        .toLowerCase()
        .split(RegExp(r'\s+'))
        .where((word) => word.isNotEmpty)
        .toList();

    // 确保 shuffledList 的长度与 originalList 相同
    shuffledList = List<String>.from(originalList)..shuffle();
    // 确保状态数组长度匹配
    wordSelectedStatus = List.generate(shuffledList.length, (_) => false);
    // 在 build 之前再次验证长度
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        if (shuffledList.length != originalList.length) {
          // 强制重新初始化
          setState(() {
            _initializeWordLists();
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const SizedBox(height: 16),
        Expanded(
          child: _buildSelectedWidgetList(),
        ),
        // SizedBox(height: 8.w),
        Expanded(
          child: _buildShuffledListWidget(),
        ),
        const SizedBox(height: 16),
        _buildPlayButton(),
        const SizedBox(height: 16),
        _buildSubmitButton(),
        // Expanded(child: WordOrderGame()),
      ],
    );
  }

  void onReorder(int oldIndex, int newIndex) {
    Console.log("oldIndex: $oldIndex, newIndex: $newIndex");
    setState(() {
      String row = selectedList.removeAt(oldIndex);
      // Widget row = selectedListWidget.removeAt(oldIndex);
      selectedList.insert(newIndex, row);
    });
  }

  _buildSelectedWidgetList() {
    selectedListWidget = selectedList
        .asMap()
        .entries
        .map((entry) => Sbutton(
              key: ValueKey('selected_${entry.key}'),
              size: SbuttonSize.small,
              onPressed: () => _moveWordToShuffled(entry),
              backgroundColor: _getWordColor(entry.value, entry.key),
              child: Text(
                entry.value,
                style: TextStyle(
                  fontSize: FontSize.title,
                ),
              ),
            ))
        .toList();

    return Container(
      decoration: BoxDecoration(
          border: Border.all(
        color:
            AppTheme.success.withValues(red: 0, green: 0, blue: 0, alpha: 0.5),
      )),
      child: Center(
        child: SingleChildScrollView(
          child: ReorderableWrap(
            spacing: 8,
            runSpacing: 4,
            padding: const EdgeInsets.all(8),
            onReorder: onReorder,
            children: selectedListWidget,
          ),
        ),
      ),
    );
  }

  Widget _buildShuffledListWidget() {
    shuffledListWidetList = shuffledList
        .asMap()
        .entries
        .map(
          (entry) => Sbutton(
            key: ValueKey('origin_${entry.key}'),
            size: SbuttonSize.small,
            onPressed: wordSelectedStatus[entry.key]
                ? null
                : () => _moveWordToSelected(entry),
            child: Text(
              entry.value,
              style: TextStyle(
                fontSize: FontSize.title,
              ),
            ),
          ),
        )
        .toList();

    return Container(
      decoration: BoxDecoration(
          border: Border.all(
        color:
            AppTheme.primary.withValues(red: 0, green: 0, blue: 0, alpha: 0.5),
      )),
      child: Center(
        child: SingleChildScrollView(
          child: Wrap(
            spacing: 12,
            runSpacing: 8,
            children: shuffledListWidetList,
          ),
        ),
      ),
    );
  }

  Widget _buildPlayButton() {
    return GetBuilder<ReviewController>(
        init: ReviewController(),
        builder: (controller) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Sbutton.icon(
                // backgroundColor: Colors.transparent,
                icon: Icon(
                  Icons.volume_up,
                  size: IconSize.titleXL,
                ),
                onPressed: () {
                  OxAudioController.to.play(
                    controller.getAssetUrl(CardAssetType.primaryAudio),
                  );
                },
              ),
              SizedBox(width: 18.w),
              Sbutton.icon(
                // backgroundColor: Colors.transparent,
                icon: Icon(
                  Icons.hourglass_top_outlined,
                  size: IconSize.titleXL,
                ),
                onPressed: () {
                  OxAudioController.to.play(
                    controller.getAssetUrl(CardAssetType.primaryAudio),
                    speed: 0.6,
                  );
                },
              ),
            ],
          );
        });
  }

  void _moveWordToSelected(MapEntry<int, String> entry) {
    if (_isProcessing) return;
    _isProcessing = true;

    try {
      _playWordAudio(entry.value);
      setState(() {
        selectedList.add(entry.value);
        wordSelectedStatus[entry.key] = true;
        isChecked = false;
        _canSubmit.value = selectedList.length == originalList.length;
      });
    } finally {
      _isProcessing = false;
    }
  }

  void _moveWordToShuffled(MapEntry<int, String> entry) {
    if (_isProcessing) return;
    _isProcessing = true;

    try {
      _playWordAudio(entry.value);
      setState(() {
        selectedList.removeAt(entry.key);
        for (int i = 0; i < shuffledList.length; i++) {
          if (shuffledList[i] == entry.value && wordSelectedStatus[i]) {
            wordSelectedStatus[i] = false;
            break;
          }
        }
        isChecked = false;
        _canSubmit.value = selectedList.length == originalList.length;
      });
    } finally {
      _isProcessing = false;
    }
  }

  // 新增音频播放辅助方法
  void _playPrimaryAudio() {
    OxAudioController.to
        .play(controller.getAssetUrl(CardAssetType.primaryAudio));
  }

  void _playWordAudio(String word) {
    final url = controller.pronunciationList
            .firstWhereNullable((e) => e.name == word.toLowerCase())
            ?.url ??
        "";
    if (url.isNotEmpty) {
      OxAudioController.to.play(url);
    }
  }

  Color _getWordColor(String word, int index) {
    Console.log('isChecked: $word, $index, ${originalList[index]} ');
    if (!isChecked)
      return Theme.of(Get.context!).colorScheme.primary; // 未检查时的默认颜色
    if (index >= originalList.length)
      return Theme.of(Get.context!).colorScheme.error;
    return word == originalList[index]
        ? Theme.of(Get.context!).colorScheme.primary
        : Theme.of(Get.context!).colorScheme.error;
  }

  void _checkOrder() {
    setState(() {
      isChecked = true; // 确保在检查时设置为 true
      for (int i = 0; i < originalList.length; i++) {
        if ((selectedList.length != originalList.length) ||
            (selectedList[i] != originalList[i])) {
          controller.playResultAudio(false);
          return;
        }
      }
      widget.onSubmit?.call();
    });
  }

  // 移除按钮禁用逻辑，始终允许点击
  Widget _buildSubmitButton() {
    return Sbutton(
      width: double.infinity,
      shape: SbuttonShape.outline,
      size: SbuttonSize.large,
      onPressed: _checkOrder, // 移除条件判断，始终可点击
      child: const Text('提交'),
    );
  }
}
