import 'package:cheestack_flt/pages/review/index.dart';
import 'package:cheestack_flt/widgets/index.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ActionBar extends GetView<ReviewController> {
  const ActionBar({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ReviewController>(
        init: ReviewController(),
        builder: (_) {
          return Row(
            children: [
              if (controller.pageIndex > 0)
                Expanded(child: _buildGetBackButton()),
              Expanded(child: _buildSubmitButton()),
            ],
          );
        });
  }

  Sbutton _buildGetBackButton() {
    return Sbutton(
      shape: SbuttonShape.outline,
      width: double.infinity,
      child: const Text("返回"),
      onPressed: () => controller.toPageIndex(plus: -1),
    );
  }

  Widget _buildSubmitButton() {
    late Widget item;
    switch (controller.pageIndex) {
      case 0:
        item = Sbutton(
          width: double.infinity,
          shape: SbuttonShape.outline,
          onPressed: controller.toPageIndex,
          child: const Text("确认"),
        );
        break;
      default:
        item = Sbutton(
          width: double.infinity,
          shape: SbuttonShape.outline,
          onPressed: controller.toPageIndex,
          child: const Text("下一步"),
        );
    }
    return item;
  }
}
