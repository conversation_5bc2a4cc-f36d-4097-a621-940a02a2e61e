part of card_editor;

/// 简化的卡片预览组件
class SimpleCardPreview extends StatelessWidget {
  final CardModelCreate cardData;
  final CardType cardType;
  
  const SimpleCardPreview({
    Key? key,
    required this.cardData,
    required this.cardType,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 预览标题
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                const Icon(Icons.visibility, size: 16, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  '预览效果',
                  fontSize: 12.0,
                  
                  color: Colors.blue,
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    _getCardTypeLabel(),
                    fontSize: 10.0,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
          ),
          
          // 预览内容
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: _buildPreviewContent(),
            ),
          ),
        ],
      ),
    );
  }

  /// 获取卡片类型标签
  String _getCardTypeLabel() {
    switch (cardType) {
      case CardType.general:
        return '基础卡片';
      case CardType.generalMarkdown:
        return 'Markdown卡片';
      case CardType.hanziWriter:
        return '汉字书写';
      case CardType.languageGeneral:
        return '语言学习';
      case CardType.languageListening:
        return '听力练习';
      case CardType.languageSpeaking:
        return '口语练习';
      case CardType.readAloud:
        return '朗读练习';
      default:
        return '未知类型';
    }
  }

  /// 构建预览内容
  Widget _buildPreviewContent() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          if (cardData.title?.isNotEmpty == true) ...[
            Text(
              cardData.title!,
              fontSize: FontSize.title,
              
            ),
            const SizedBox(height: 12),
          ],
          
          // 问题
          if (cardData.question?.isNotEmpty == true) ...[
            const Text(
              '问题:',
              fontSize: 12.0,
              color: Colors.grey,
            ),
            const SizedBox(height: 4),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                cardData.question!,
                fontSize: FontSize.body,
              ),
            ),
            const SizedBox(height: 16),
          ],
          
          // 答案
          if (cardData.answer?.isNotEmpty == true) ...[
            const Text(
              '答案:',
              fontSize: 12.0,
              color: Colors.grey,
            ),
            const SizedBox(height: 4),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
              ),
              child: Text(
                cardData.answer!,
                fontSize: FontSize.body,
                color: Colors.green.shade700,
              ),
            ),
            const SizedBox(height: 16),
          ],
          
          // 卡片类型特殊预览
          _buildTypeSpecificPreview(),
          
          // 资源预览
          if (cardData.cardAssets.isNotEmpty) ...[
            const SizedBox(height: 16),
            _buildAssetsPreview(),
          ],
        ],
      ),
    );
  }

  /// 构建特定类型的预览
  Widget _buildTypeSpecificPreview() {
    switch (cardType) {
      case CardType.hanziWriter:
        return _buildHanziPreview();
      case CardType.languageGeneral:
        return _buildLanguagePreview();
      case CardType.readAloud:
        return _buildReadAloudPreview();
      default:
        return const SizedBox.shrink();
    }
  }

  /// 汉字书写预览
  Widget _buildHanziPreview() {
    if (cardData.title?.isNotEmpty != true) return const SizedBox.shrink();
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          const Text(
            '请书写汉字:',
            fontSize: 12.0,
            color: Colors.grey,
          ),
          const SizedBox(height: 8),
          Text(
            cardData.title!,
            fontSize: 48,
            
          ),
        ],
      ),
    );
  }

  /// 语言学习预览
  Widget _buildLanguagePreview() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.purple.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Row(
        children: [
          Icon(Icons.quiz, color: Colors.purple),
          SizedBox(width: 8),
          Text(
            '选择题模式',
            fontSize: 14.0,
            color: Colors.purple,
          ),
        ],
      ),
    );
  }

  /// 朗读练习预览
  Widget _buildReadAloudPreview() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.teal.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Row(
        children: [
          Icon(Icons.record_voice_over, color: Colors.teal),
          SizedBox(width: 8),
          Text(
            '朗读练习模式',
            fontSize: 14.0,
            color: Colors.teal,
          ),
        ],
      ),
    );
  }

  /// 构建资源预览
  Widget _buildAssetsPreview() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '附件:',
          fontSize: 12.0,
          color: Colors.grey,
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: cardData.cardAssets.map((asset) {
            if (asset.type?.contains('image') == true) {
              return Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.grey.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.image, color: Colors.grey),
              );
            } else if (asset.type?.contains('audio') == true) {
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.audiotrack, size: 16, color: Colors.blue),
                    SizedBox(width: 4),
                    Text('音频',
                        style: TextStyle(fontSize: 10.0, color: Colors.blue)),
                  ],
                ),
              );
            }
            return const SizedBox.shrink();
          }).toList(),
        ),
      ],
    );
  }
}
