part of card_editor;

/// 针对不同卡片类型的专门编辑组件
class TypeSpecificEditor extends StatelessWidget {
  final CardType cardType;
  final CardEditorController controller;
  
  const TypeSpecificEditor({
    Key? key,
    required this.cardType,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    switch (cardType) {
      case CardType.hanziWriter:
        return _buildHanziWriterEditor();
      case CardType.languageGeneral:
        return _buildLanguageGeneralEditor();
      case CardType.languageListening:
        return _buildLanguageListeningEditor();
      case CardType.readAloud:
        return _buildReadAloudEditor();
      case CardType.generalMarkdown:
        return _buildMarkdownEditor();
      default:
        return _buildGeneralEditor();
    }
  }

  /// 汉字书写编辑器
  Widget _buildHanziWriterEditor() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 提示信息
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.orange.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.orange.withOpacity(0.3)),
          ),
          child: const Row(
            children: [
              Icon(Icons.edit, color: Colors.orange, size: 16),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  '汉字书写卡片：学习者需要根据提示书写汉字。标题将作为要书写的汉字显示。',
                  style: TextStyle(
                    fontSize: 12.0,
                    color: Colors.orange,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        
        // 汉字输入
        OxInputField(
          controller: controller.baseInfoController,
          label: "要书写的汉字",
          hintText: "输入一个或多个汉字",
          maxLines: 1,
          onChanged: (value) {
            controller.updatePreviewData();
          },
        ),
        const SizedBox(height: 16),
        
        // 提示信息输入
        OxInputField(
          controller: controller.frontTextController,
          label: "书写提示（可选）",
          hintText: "例如：拼音、释义等提示信息",
          maxLines: 2,
          onChanged: (value) {
            controller.updatePreviewData();
          },
        ),
      ],
    );
  }

  /// 语言学习编辑器
  Widget _buildLanguageGeneralEditor() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 提示信息
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.purple.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.purple.withOpacity(0.3)),
          ),
          child: const Row(
            children: [
              Icon(Icons.quiz, color: Colors.purple, size: 16),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  '语言学习卡片：以选择题形式呈现，学习者从选项中选择正确答案。',
                  style: TextStyle(
                    fontSize: 12.0,
                    color: Colors.purple,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        
        // 问题输入
        OxInputField(
          controller: controller.frontTextController,
          label: "问题",
          hintText: "输入要提问的内容",
          maxLines: 3,
          onChanged: (value) {
            controller.updatePreviewData();
          },
        ),
        const SizedBox(height: 16),
        
        // 正确答案输入
        OxInputField(
          controller: controller.backTextController,
          label: "正确答案",
          hintText: "输入正确答案（将作为选项A显示）",
          maxLines: 2,
          onChanged: (value) {
            controller.updatePreviewData();
          },
        ),
        const SizedBox(height: 16),
        
        // 干扰选项提示
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.grey.withOpacity(0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: const Text(
            '💡 提示：系统会自动生成其他干扰选项，您只需要输入正确答案即可。',
            style: TextStyle(
              fontSize: 11.0,
              color: Colors.grey,
            ),
          ),
        ),
      ],
    );
  }

  /// 听力练习编辑器
  Widget _buildLanguageListeningEditor() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 提示信息
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue.withOpacity(0.3)),
          ),
          child: const Row(
            children: [
              Icon(Icons.headphones, color: Colors.blue, size: 16),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  '听力练习卡片：学习者听音频后选择正确答案。需要上传音频文件。',
                  style: TextStyle(
                    fontSize: 12.0,
                    color: Colors.blue,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        
        // 音频上传提示
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.amber.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.amber.withOpacity(0.3)),
          ),
          child: const Row(
            children: [
              Icon(Icons.upload_file, color: Colors.amber, size: 16),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  '请在"附件"页面上传音频文件',
                  style: TextStyle(
                    fontSize: 12.0,
                    color: Colors.amber,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        
        // 问题描述
        OxInputField(
          controller: controller.frontTextController,
          label: "听力问题",
          hintText: "描述学习者需要从音频中获取什么信息",
          maxLines: 2,
          onChanged: (value) {
            controller.updatePreviewData();
          },
        ),
        const SizedBox(height: 16),
        
        // 正确答案
        OxInputField(
          controller: controller.backTextController,
          label: "正确答案",
          hintText: "输入听力材料的正确答案",
          maxLines: 2,
          onChanged: (value) {
            controller.updatePreviewData();
          },
        ),
      ],
    );
  }

  /// 朗读练习编辑器
  Widget _buildReadAloudEditor() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 提示信息
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.teal.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.teal.withOpacity(0.3)),
          ),
          child: const Row(
            children: [
              Icon(Icons.record_voice_over, color: Colors.teal, size: 16),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  '朗读练习卡片：学习者需要朗读指定的文本内容。',
                  style: TextStyle(
                    fontSize: 12.0,
                    color: Colors.teal,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        
        // 朗读内容
        OxInputField(
          controller: controller.frontTextController,
          label: "朗读内容",
          hintText: "输入需要朗读的文本",
          maxLines: 4,
          onChanged: (value) {
            controller.updatePreviewData();
          },
        ),
        const SizedBox(height: 16),
        
        // 朗读要求
        OxInputField(
          controller: controller.backTextController,
          label: "朗读要求（可选）",
          hintText: "例如：注意语调、重音等要求",
          maxLines: 2,
          onChanged: (value) {
            controller.updatePreviewData();
          },
        ),
      ],
    );
  }

  /// Markdown编辑器
  Widget _buildMarkdownEditor() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 提示信息
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.indigo.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.indigo.withOpacity(0.3)),
          ),
          child: const Row(
            children: [
              Icon(Icons.code, color: Colors.indigo, size: 16),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Markdown卡片：支持富文本格式，可以使用Markdown语法。',
                  style: TextStyle(
                    fontSize: 12.0,
                    color: Colors.indigo,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        
        // Markdown内容
        OxInputField(
          controller: controller.frontTextController,
          label: "Markdown内容",
          hintText: "支持**粗体**、*斜体*、`代码`等Markdown语法",
          maxLines: 6,
          onChanged: (value) {
            controller.updatePreviewData();
          },
        ),
      ],
    );
  }

  /// 通用编辑器
  Widget _buildGeneralEditor() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 问题输入
        OxInputField(
          controller: controller.frontTextController,
          label: "问题",
          hintText: "输入卡片正面的问题或内容",
          maxLines: 3,
          onChanged: (value) {
            controller.updatePreviewData();
          },
        ),
        const SizedBox(height: 16),
        
        // 答案输入
        OxInputField(
          controller: controller.backTextController,
          label: "答案",
          hintText: "输入卡片背面的答案或解释",
          maxLines: 3,
          onChanged: (value) {
            controller.updatePreviewData();
          },
        ),
      ],
    );
  }
}
