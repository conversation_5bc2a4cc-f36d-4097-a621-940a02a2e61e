part of card_editor;

class MultiBookSelectSearchBottomSheet extends StatelessWidget {
  const MultiBookSelectSearchBottomSheet({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CardEditorController>(
      init: CardEditorController(),
      builder: (controller) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.w),
              child: const Text(
                "添加到卡片本",
                
              ),
            ),
            OxInputField(
              hintText:
                  // controller.selectedBookItems.map((e) => e.label).join(","),
                  controller.bookItems
                      .where((element) => element.selected == true)
                      .map((e) => e.label)
                      .join(","),
              readOnly: true,
              iconData: Icons.arrow_drop_down,
              onTap: () {
                // controller.fetchBookList();
                showBottomSheet(controller);
              },
            )
          ],
        );
      },
    );
  }

  showBottomSheet(CardEditorController controller) async {
    await showModalBottomSheet(
        isScrollControlled: true,
        context: Get.context!,
        builder: (context) => CustomSearchBottomSheet(
              onChange: controller.onBookSelected,
              onSearch: controller.onInputValueChanged,
              items: controller.bookItems,
            )
        // builder: (context) => customSearchBottomSheet(),
        );
  }
}

class CustomSearchBottomSheet extends StatefulWidget {
  final Function(String?) onSearch;
  final Function(bool?, int) onChange;
  final List<CustomSelectItem> items;
  const CustomSearchBottomSheet({
    super.key,
    required this.items,
    required this.onSearch,
    required this.onChange,
  });

  @override
  CustomSearchBottomSheetState createState() => CustomSearchBottomSheetState();
}

class CustomSearchBottomSheetState extends State<CustomSearchBottomSheet> {
  List<CustomSelectItem> items = [];

  @override
  void initState() {
    super.initState();
    items = widget.items;

    /// 默认选中的排在前面
    items.sort((a, b) => a.selected ? -1 : 1);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: SizedBox(
        height: 0.7 * 812.w,
        child: Column(
          children: [
            SizedBox(height: 20.w),
            Row(
              children: [
                Expanded(
                    child: SinputSearch(
                  hintText: LocaleKeys.formSearch.tr,
                  // onChanged: controller.onInputValueChanged,
                  onChanged: (val) {
                    setState(() {
                      if (val == "") {
                        items = widget.items;
                      } else {
                        items = widget.items
                            .where((element) =>
                                element.label?.contains(val) == true)
                            .toList();
                      }

                      /// 默认选中的排在前面
                      items.sort((a, b) => a.selected ? -1 : 1);
                    });
                  },
                )),
              ],
            ),
            Expanded(
              child: ListView.builder(
                itemCount: items.length,
                itemBuilder: (context, index) {
                  return CheckboxListTile(
                    title: Text(items[index].label ?? ""),
                    value: items[index].selected,
                    onChanged: (value) {
                      setState(() {
                        widget.onChange(value, index);
                        items[index].selected = value ?? false;
                        // controller.onBookSelected(value ?? false, index);
                      });
                    },
                  );
                },
              ),
            ),
            Sbutton(
              onPressed: Get.back,
              width: double.infinity,
              child: const Text('完成'),
            ),
          ],
        ),
      ),
    );
  }
}
