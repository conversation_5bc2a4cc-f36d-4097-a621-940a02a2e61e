part of editing_book_select;

class CardEditListController extends PullToRefreshController<CardModel> {
  CardEditListController();

  /// 输入框控制器
  TextEditingController textEditingController = TextEditingController();

  /// 输入框内容改变回调
  Function(String)? onInputValueChanged;
  // book id
  int? bookId;

  List<BookModel> bookList = [];

  _initData() async {
    await fetchRefreshDataList();
    await getBookList();
  }

  @override
  void onInit() {
    final args = Get.arguments;
    if (args is int) bookId = args;

    Console.log(args);
    onInputValueChanged = debounceFnWithArgs(fetchCardList, delay: 500);
    super.onInit();
  }

  @override
  void onReady() async {
    super.onReady();
    _initData();
  }

  Future fetchCardList(String val) async {
    // 优先使用本地数据服务
    if (Get.isRegistered<CardDataService>()) {
      try {
        final cardDataService = CardDataService.to;
        if (bookId != null) {
          if (val.isEmpty) {
            // 获取书籍的所有卡片
            refreshDataList = await cardDataService.getBookCards(
              bookId!,
              limit: refreshConfigModel.limit,
              offset: refreshConfigModel.skip,
            );
          } else {
            // 搜索卡片
            refreshDataList = await cardDataService.searchCards(
              val,
              bookId: bookId,
              limit: refreshConfigModel.limit,
              offset: refreshConfigModel.skip,
            );
          }
        } else {
          refreshDataList = [];
        }
        update();
      } catch (e) {
        final friendlyMessage = ErrorHandler.getUserFriendlyMessage(e);
        ShowToast.fail(friendlyMessage);
      }
    } else {
      // 回退到网络请求
      Map<String, dynamic> filters = {
        'title__icontains': val,
        'books': bookId,
      };

      await asyncRequest(
        () => OxHttp.to.get(HttpUrl.cards, queryParameters: {
          "skip": refreshConfigModel.skip,
          "limit": refreshConfigModel.limit,
          "filters": jsonEncode(filters),
        }),
        onSuccess: (data) {
          refreshDataList =
              data.map<CardModel>((x) => CardModel.fromJson(x)).toList();
          update();
        },
        onFailure: (error) {
          final friendlyMessage = ErrorHandler.getUserFriendlyMessage(error);
          ShowToast.fail(friendlyMessage);
        },
      );
    }
  }

  @override
  Future fetchRefreshDataList() async {
    Map<String, dynamic> filters = {"books": bookId};

    /// 请求数据
    await asyncRequest(
      () => OxHttp.to.get(HttpUrl.cards, queryParameters: {
        "skip": refreshConfigModel.skip,
        "limit": refreshConfigModel.limit,
        "created_at__lte": refreshConfigModel.utcString,
        "filters": jsonEncode(filters),
      }),
      onSuccess: (data) {
        List<CardModel> refreshModelList =
            data.map<CardModel>((x) => CardModel.fromJson(x)).toList();
        // 判断是否已经加载完毕
        refreshConfigModel.finish =
            refreshModelList.length < (refreshConfigModel.limit);
        // 添加数据到刷新列表
        refreshDataList.addAll(refreshModelList);

        update();
      },
      onFailure: (error) {
        loadFailed();
        ShowToast.fail(error?.msg ?? "未知错误");
      },
    );
  }

  Future toCardEditPage({CardModel? cardModel}) async {
    await Get.toNamed(AppRoutes.cardEditor, arguments: cardModel ?? bookId)
        ?.then((e) async {
      await onRefresh();
    }).then((e) {
      update();
    });
  }

  onDeleteCard(CardModel cardModel) {
    Console.log(cardModel.id);

    Sdialog.show(
      cancel: const Text("取消"),
      onCancel: () => Get.back(),
      confirm: const Text("确认"),
      onConfirm: () async {
        await asyncRequest(
          () => OxHttp.to.delete("${HttpUrl.cards}/${cardModel.id}"),
          onSuccess: (data) {
            refreshDataList
                .removeWhere((element) => element.id == cardModel.id);
            update();
          },
          onFailure: (error) {
            final friendlyMessage = ErrorHandler.getUserFriendlyMessage(error);
            ShowToast.fail(friendlyMessage);
          },
        );
        Get.back();
      },
      builder: (BuildContext context) {
        return const Text("确定删除吗? 该操作不可逆" ?? "");
      },
    );
  }

  void toEditingBookSelect() async {
    await Get.toNamed(AppRoutes.cardEditList);
    await onRefresh();
    update();
  }

  String? getAssetUrl(
    CardAssetType type,
    CardModel curCard,
  ) {
    String? url = curCard.cardAssets
        ?.firstWhereNullable((e) => e.type == type.value)
        ?.url;
    return url;
  }

  /// 函数用于跳转页面到卡片详情页
  /// @param id int: 卡片id
  /// 接收页面返回值, 判断返回值是否为类型为`true`
  /// 如果返回值为`true`则刷新页面数据
  Future toCardInfoPage(int index) async {
    CardModel cardModel = refreshDataList[index];

    // 获取返回数据
    final result = await Get.toNamed(AppRoutes.review, arguments: {
      "type": StudyType.info,
      "cardModel": cardModel,
    });
    Console.log(result);
    // 如果返回值是`true`，则刷新页面
    if (result == true) {
      await onRefresh();
    }
  }

  Future toBookEditPage() async {
    var result = await Get.toNamed(AppRoutes.bookEditor);
    if (result is BookModel) {
      bookList.add(result);
    }
    await onRefresh();
    update();
  }

  Future toBookInfoPage() async {
    BookModel? bookModel = bookList.firstWhereOrNull(
        (element) => element.id == AuthController.to.usr.config?.editingBookId);
    if (bookModel == null) {
      ShowToast.fail("未找到该笔记本");
      return;
    }

    Get.toNamed(AppRoutes.bookInfo, arguments: bookModel);
  }

  Future getBookList() async {
    // 优先使用本地数据服务
    if (Get.isRegistered<BookDataService>()) {
      try {
        final bookDataService = BookDataService.to;
        bookList = await bookDataService.getUserBooks(
          limit: refreshConfigModel.limit,
          offset: refreshConfigModel.skip,
        );
        update();
      } catch (e) {
        final friendlyMessage = ErrorHandler.getUserFriendlyMessage(e);
        ShowToast.fail(friendlyMessage);
      }
    } else {
      // 回退到网络请求
      Map<String, dynamic> filters = {
        'user_id': AuthController.to.usr.user?.id,
      };
      await asyncRequest(
        () => OxHttp.to.get(HttpUrl.books, queryParameters: {
          "skip": refreshConfigModel.skip,
          "limit": refreshConfigModel.limit,
          "filters": jsonEncode(filters),
        }),
        onSuccess: (data) {
          bookList = data.map<BookModel>((x) => BookModel.fromJson(x)).toList();
          update();
        },
        onFailure: (error) {
          final friendlyMessage = ErrorHandler.getUserFriendlyMessage(error);
          ShowToast.fail(friendlyMessage);
        },
      );
    }
  }

  // Future init() async {
  //   update();
  // }

  // void onTap() {}

  // @override
  // void onInit() {
  //   onInputValueChanged = debounceFnWithArgs(fetchBookList, delay: 1000);

  //   super.onInit();
  // }

  // _initData() async {
  //   await fetchRefreshDataList();
  //   // await getBookList();
  // }

  // @override
  // void onReady() async {
  //   super.onReady();
  //   await _initData();
  // }

  // @override
  // Future fetchRefreshDataList() async {
  //   Map<String, dynamic> filters = {"user_id": AuthController.to.usr.user?.id};

  //   /// 请求数据
  //   await asyncRequest(
  //     () => HttpUtils.to.get(HttpUrl.books, queryParameters: {
  //       "skip": refreshConfigModel.skip,
  //       "limit": refreshConfigModel.limit,
  //       "created_at__lte": refreshConfigModel.utcString,
  //       "filters": jsonEncode(filters),
  //     }),
  //     onSuccess: (data) {
  //       List<BookModel> refreshModelList =
  //           data.map<BookModel>((x) => BookModel.fromJson(x)).toList();
  //       // 判断是否已经加载完毕
  //       refreshConfigModel.finish =
  //           refreshModelList.length < (refreshConfigModel.limit);
  //       // 添加数据到刷新列表
  //       refreshDataList.addAll(refreshModelList);
  //       update();
  //     },
  //     onFailure: (error) {
  //       ShowToast.fail(error?.msg ?? "未知错误");
  //     },
  //   );
  // }

  // Future toBookEditPage({BookModel? bookModel}) async {
  //   await Get.toNamed(AppRoutes.bookEditor, arguments: bookModel)
  //       ?.then((e) async {
  //     await onRefresh();
  //   }).then((e) {
  //     update();
  //   });
  // }

  // Future toBookInfoPage({BookModel? bookModel}) async {
  //   await Get.toNamed(AppRoutes.bookInfo, arguments: bookModel)
  //       ?.then((e) async {
  //     await onRefresh();
  //   }).then((e) {
  //     update();
  //   });
  // }

  // onDeleteBook(BookModel bookModel) {
  //   ChvDialog.show(
  //     cancel: const Stext("取消"),
  //     onCancel: () => Get.back(),
  //     confirm: const Stext("确认"),
  //     onConfirm: () async {
  //       await asyncRequest(
  //         () => HttpUtils.to.delete("${HttpUrl.books}/${bookModel.id}"),
  //         onSuccess: (data) {
  //           refreshDataList
  //               .removeWhere((element) => element.id == bookModel.id);
  //           if (AuthController.to.usr.config?.editingBookId == bookModel.id) {
  //             if (refreshDataList.isEmpty) {
  //               AuthController.to.usr.config?.editingBookId = null;
  //               AuthController.to.usr.config?.editingBook = null;
  //               AuthController.to.updateUsr();
  //             } else {
  //               AuthController.to.usr.config?.editingBookId =
  //                   refreshDataList.firstNullable?.id;
  //               AuthController.to.usr.config?.editingBook = EditingBook(
  //                   id: refreshDataList.firstNullable?.id,
  //                   name: refreshDataList.firstNullable?.name);
  //               AuthController.to.updateUsr();
  //             }
  //           }
  //           update();
  //         },
  //         onFailure: (error) {
  //           ShowToast.fail(error?.msg ?? "未知错误");
  //         },
  //       );
  //       Get.back();
  //     },
  //     builder: (BuildContext context) {
  //       return const Stext("确定删除吗? 该操作不可逆");
  //     },
  //   );
  // }

  // selectEditingBook(int id) async {
  //   ShowToast.loading();
  //   AuthController.to.usr.config?.editingBookId = id;
  //   await updateConfigModel();
  //   AuthController.to.update();
  //   Future.delayed(const Duration(milliseconds: 500)).then((value) {
  //     update();
  //     ShowToast.dismiss();
  //   Get.back();

  //   });
  // }

  // Future fetchBookList(String val) async {
  //   Map<String, dynamic> filters = {
  //     'name__icontains': val,
  //   };

  //   await asyncRequest(
  //     () => HttpUtils.to.get(HttpUrl.books, queryParameters: {
  //       "skip": refreshConfigModel.skip,
  //       "limit": refreshConfigModel.limit,
  //       "filters": jsonEncode(filters),
  //     }),
  //     onSuccess: (data) {
  //       refreshDataList =
  //           data.map<BookModel>((x) => BookModel.fromJson(x)).toList();
  //       update();
  //     },
  //     onFailure: (error) {
  //       ShowToast.fail(error?.msg ?? "未知错误");
  //     },
  //   );
  // }

  // Future<void> updateConfigModel() async {
  //   await asyncRequest(
  //     () => HttpUtils.to
  //         .put(HttpUrl.config, data: AuthController.to.usr.config?.toJson()),
  //     onSuccess: (data) {
  //       AuthController.to.usr.config = ConfigModel.fromJson(data);
  //       AuthController.to.updateUsr();
  //     },
  //     onFailure: (error) {
  //       ShowToast.fail(error?.msg ?? "未知错误");
  //     },
  //   );
  // }
}
