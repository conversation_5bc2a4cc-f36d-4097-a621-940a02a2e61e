import 'package:cheestack_flt/models/index.dart';
import 'package:flutter/material.dart';

class BookItemBuilder extends StatelessWidget {
  final List<BookModel> dataList;
  final void Function(BookModel book) onTap;

  const BookItemBuilder(
      {super.key, required this.dataList, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: dataList.length,
      itemBuilder: (context, index) {
        return CustomCard.common(
          title: Text(dataList[index].name ?? "" ?? ""),
          image: Simage(
            url: dataList[index].cover ?? "",
            fit: BoxFit.contain,
          ),
          subtitle: Text(
            dataList[index].brief ?? "",
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          label: Text(
            dataList[index].user?.username ?? "",
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          onTap: () {
            onTap(dataList[index]);
          },
        );
      },
    );
  }
}
