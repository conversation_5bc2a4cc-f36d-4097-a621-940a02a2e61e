part of listening;

class ListeningPage extends StatefulWidget {
  const ListeningPage({Key? key}) : super(key: key);

  @override
  State<ListeningPage> createState() => _ListeningPageState();
}

class _ListeningPageState extends State<ListeningPage>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return GetBuilder<ListeningController>(
      init: ListeningController(),
      builder: (controller) {
        return Scaffold(
          appBar: _buildAppBar(),
          body: SafeArea(
            minimum: EdgeInsets.all(AppTheme.margin),
            child: _buildView(),
          ),
        );
      },
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
        title: Text(
      LocaleKeys.listeningTitle.tr,
      fontSize: FontSize.title,
    ));
  }

  /// 主视图
  Widget _buildView() {
    return const Column(
      children: [
        Expanded(child: SaudioPlayListView()),
      ],
    );
  }

  @override
  bool get wantKeepAlive => true;
}
