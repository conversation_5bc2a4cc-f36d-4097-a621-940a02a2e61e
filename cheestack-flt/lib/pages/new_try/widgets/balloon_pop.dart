part of '../index.dart';

/// 游戏相关常量
class GameConstants {
  static const double balloonWidth = 120.0;
  static const double balloonHeight = 140.0;
  static const double particleAnimationDuration = 1000;
  static const int correctScore = 10;
  static const int wrongScore = -5;
  static const int comboScore2 = 2;
  static const int comboScore3 = 3;
  static const int comboScore4Plus = 5;
}

/// 定义音标数据结构
class PhonemeData {
  final String phoneme;
  final String audioPath;

  const PhonemeData({
    required this.phoneme,
    required this.audioPath,
  });
}

/// 游戏统计数据
class GameStats {
  int currentScore = 0;
  int comboCount = 0;
  int maxCombo = 0;
  int correctCount = 0;
  int wrongCount = 0;
  int highScore = 0;

  double get accuracy => correctCount + wrongCount > 0
      ? (correctCount / (correctCount + wrongCount) * 100)
      : 0.0;
}

/// 气球游戏组件 - 用于练习音标发音
class BalloonGame extends StatelessWidget {
  final List<PhonemeData> phonemeList;

  const BalloonGame({
    super.key,
    required this.phonemeList,
  });

  @override
  Widget build(BuildContext context) {
    return GameWidget(
      game: BalloonFlameGame(
        phonemeList: phonemeList,
      ),
    );
  }
}

class BalloonFlameGame extends FlameGame with TapCallbacks {
  final List<PhonemeData> phonemeList;
  late int currentIndex;
  late List<BalloonComponent> balloons;
  final Random random = Random();
  final List<Color> _usedColors = [];

  // 当前剩余的音标列表
  late List<PhonemeData> _remainingPhonemes;
  // 已经正确选择的音标列表
  final List<PhonemeData> _correctPhonemes = [];

  static const int maxBalloons = 6;
  bool _isGameComplete = false;
  late final OxAudioController _audioController;

  final GameStats gameStats = GameStats();
  late final ScoreDisplay scoreDisplay;
  late final ComboDisplay comboDisplay;

  BalloonFlameGame({
    required this.phonemeList,
  }) {
    _audioController = OxAudioController.to;
    // 初始化剩余音标列表并随机化
    _remainingPhonemes = List.from(phonemeList)..shuffle();
    // 随机化初始的 currentIndex
    currentIndex = random.nextInt(_remainingPhonemes.length);
  }

  @override
  Future<void> onLoad() async {
    add(SkyBackground());
    balloons = [];
    _usedColors.clear();
    _isGameComplete = false;

    // 初始播放当前音标的读音时设置强制听写状态
    await _playCurrentPhonemeAudio(isForceListening: true);

    // 初始显示气球 - 修改为一次性选择不重复的音标
    final initialPhonemes = List<PhonemeData>.from(_remainingPhonemes)
      ..shuffle();
    final count = min(maxBalloons, initialPhonemes.length);

    for (int i = 0; i < count; i++) {
      final phoneme = initialPhonemes[i];
      final entryPosition = _getRandomEntryPosition();
      _getRandomTargetPosition();

      final component = BalloonComponent(
        phoneme: phoneme.phoneme,
        position: entryPosition,
        size: Vector2(GameConstants.balloonWidth, GameConstants.balloonHeight),
        usedColors: _usedColors,
        isCorrect: phoneme.phoneme == phonemeList[currentIndex].phoneme,
      );

      component.onPop = (isCorrect, position) async {
        // 更新分数
        _updateScore(isCorrect, position);

        if (isCorrect) {
          await _playCorrectSound();
          // 从剩余列表中移除并添加到正确列表
          _remainingPhonemes.removeWhere(
              (p) => p.phoneme == phonemeList[currentIndex].phoneme);
          _correctPhonemes.add(phonemeList[currentIndex]);

          // 移除当前气球
          balloons.remove(component);
          component.removeFromParent();

          // 先完成游戏状态更新，再播放新音标的音频
          await _onGameComplete();
        } else {
          await _playWrongSound();
          // 移除当前气球
          balloons.remove(component);
          component.removeFromParent();
          // 立即添加新气球并重播当前正确音标的音频
          _addNewBalloon();
          await _playCurrentPhonemeAudio(isForceListening: true);
        }
      };

      balloons.add(component);
      add(component);
    }

    _repositionBalloons();

    // 加载历史最高分
    await _loadHighScore();

    // 添加分数显示组件
    scoreDisplay = ScoreDisplay(gameStats);
    add(scoreDisplay);

    // 添加连击显示组件 - 修改位置到分数的右侧
    comboDisplay = ComboDisplay(gameStats);
    add(comboDisplay);

    // 添加安全区域组件来防止气球遮挡
    add(SafeZoneComponent());
  }

  Future<void> _playCurrentPhonemeAudio({bool isForceListening = true}) async {
    try {
      // 添加日志
      await _audioController.play(
        phonemeList[currentIndex].audioPath,
        isForceListening: isForceListening,
      );
    } catch (e) {
      debugPrint('音频播放失败: $e');
    }
  }

  void _addNewBalloon() {
    if (_isGameComplete) {
      return;
    }

    // 从剩余音标中随机选择一个
    if (_remainingPhonemes.isEmpty) {
      _isGameComplete = true;
      return;
    }

    // 获取当前未显示的音标列表
    final availablePhonemes = _remainingPhonemes
        .where((phoneme) =>
            !balloons.any((balloon) => balloon.phoneme == phoneme.phoneme))
        .toList();

    if (availablePhonemes.isEmpty) {
      return; // 如果没有可用的音标，不添加新气球
    }

    // 随机选择一个未显示的音标
    final newPhoneme =
        availablePhonemes[random.nextInt(availablePhonemes.length)];

    final entryPosition = _getRandomEntryPosition();
    _getRandomTargetPosition();

    final component = BalloonComponent(
      phoneme: newPhoneme.phoneme,
      position: entryPosition,
      size: Vector2(GameConstants.balloonWidth, GameConstants.balloonHeight),
      usedColors: _usedColors,
      isCorrect: newPhoneme.phoneme == phonemeList[currentIndex].phoneme,
    );

    component.onPop = (isCorrect, position) async {
      // 更新分数
      _updateScore(isCorrect, position);

      if (isCorrect) {
        await _playCorrectSound();
        // 从剩余列表中移除并添加到正确列表
        _remainingPhonemes
            .removeWhere((p) => p.phoneme == phonemeList[currentIndex].phoneme);
        _correctPhonemes.add(phonemeList[currentIndex]);

        // 移除当前气球
        balloons.remove(component);
        component.removeFromParent();

        // 先完成游戏状态更新，再播放新音标的音频
        await _onGameComplete();
      } else {
        await _playWrongSound();
        // 移除当前气球
        balloons.remove(component);
        component.removeFromParent();
        // 立即添加新气球并重播当前正确音标的音频
        _addNewBalloon();
        await _playCurrentPhonemeAudio(isForceListening: true);
      }
    };

    Future.microtask(() {
      balloons.add(component);
      add(component);
    });
  }

  Future<void> _playCorrectSound() async {
    try {
      await _audioController.play(
        AssetPath.success,
        isForceListening: true,
      );
    } catch (e) {
      debugPrint('正确音效播放失败1: $e');
    }
  }

  Future<void> _playWrongSound() async {
    try {
      await _audioController.play(
        AssetPath.wrong,
        isForceListening: true,
      );
    } catch (e) {
      debugPrint('错误音效播放失败: $e');
    }
  }

  Future<void> _onGameComplete() async {
    if (_remainingPhonemes.isEmpty) {
      _isGameComplete = true;

      // 保存最高分
      await _saveHighScore();

      // 弹出对话框
      showDialog(
        context: Get.context!,
        builder: (BuildContext context) {
          return Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20.0),
            ),
            child: Container(
              padding: const EdgeInsets.all(20.0),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20.0),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  const Icon(
                    Icons.check_circle_outline,
                    color: Colors.green,
                    size: 60,
                  ),
                  const SizedBox(height: 20),
                  const Text(
                    '恭喜！',
                    style: TextStyle(
                      fontSize: 24,
                      
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    '本次得分: ${gameStats.currentScore}',
                    style: const TextStyle(fontSize: 18),
                  ),
                  if (gameStats.currentScore >= gameStats.highScore)
                    const Text(
                      '新纪录！',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.orange,
                        
                      ),
                    ),
                  const SizedBox(height: 10),
                  Text(
                    '最高连击: ${gameStats.maxCombo}',
                    style: const TextStyle(fontSize: 16),
                  ),
                  Text(
                    '准确率: ${gameStats.accuracy.toStringAsFixed(1)}%',
                    style: const TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 20),
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      Navigator.of(context).pop();
                    },
                    child: const Text(
                      '返回',
                      style: TextStyle(
                        color: Colors.blue,
                        
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
      return;
    }

    // 从剩余音标中随机选择一个作为新的目标
    currentIndex = phonemeList
        .indexOf(_remainingPhonemes[random.nextInt(_remainingPhonemes.length)]);

    // 重置游戏状态
    _usedColors.clear();
    _isGameComplete = false;

    // 不再补充新气球，让剩余的气球继续存在
    // 更新所有剩余气球的isCorrect状态
    for (final balloon in balloons) {
      balloon.updateCorrectStatus(
          phonemeList[currentIndex].phoneme == balloon.phoneme);
    }

    // 在所有状态更新完成后，播放新的音标音频，并设置强制听写状态为 true
    await _playCurrentPhonemeAudio(isForceListening: true);
  }

  Vector2 _getRandomEntryPosition() {
    final side = random.nextInt(4); // 0: 上, 1: 右, 2: 下, 3: 左
    final screenWidth = size.x;
    final screenHeight = size.y;

    switch (side) {
      case 0: // 上边
        return Vector2(
          random.nextDouble() * (screenWidth - GameConstants.balloonWidth),
          -GameConstants.balloonHeight,
        );
      case 1: // 右边
        return Vector2(
          screenWidth + GameConstants.balloonWidth,
          random.nextDouble() * (screenHeight - GameConstants.balloonHeight),
        );
      case 2: // 下边
        return Vector2(
          random.nextDouble() * (screenWidth - GameConstants.balloonWidth),
          screenHeight + GameConstants.balloonHeight,
        );
      default: // 左边
        return Vector2(
          -GameConstants.balloonWidth,
          random.nextDouble() * (screenHeight - GameConstants.balloonHeight),
        );
    }
  }

  Vector2 _getRandomTargetPosition() {
    const padding = 150.0;
    return Vector2(
      padding + random.nextDouble() * (size.x - 2 * padding),
      // 增加顶部安全距离
      SafeZoneComponent.safeZoneHeight +
          random.nextDouble() *
              (size.y - SafeZoneComponent.safeZoneHeight - padding),
    );
  }

  void _repositionBalloons() {
    final screenWidth = size.x;
    final screenHeight = size.y;

    for (int i = 0; i < balloons.length; i++) {
      balloons[i].position = Vector2(
        random.nextDouble() * (screenWidth - GameConstants.balloonWidth),
        // 确保初始位置也不在安全区域内
        SafeZoneComponent.safeZoneHeight +
            random.nextDouble() *
                (screenHeight -
                    SafeZoneComponent.safeZoneHeight -
                    GameConstants.balloonHeight),
      );
      balloons[i].velocity = _getRandomVelocity();
    }
  }

  Vector2 _getRandomVelocity() {
    return Vector2(
      (random.nextDouble() - 0.5) * 100, // 增加速度范围
      (random.nextDouble() - 0.5) * 100,
    );
  }

  @override
  void update(double dt) {
    super.update(dt);

    for (final balloon in balloons) {
      // 降低更新频率，使运动更加平滑
      balloon.update(dt * 0.8); // 添加时间缩放因子

      // 调整减速参数，使运动更加平缓
      if (balloon.velocity.length > 15) {
        // 从20降低到15
        balloon.velocity *= 0.999; // 从0.998改为0.999，使减速更加缓慢
      }

      // 检查并修正边界位置
      _handleBoundaryCollision(balloon);

      // 检查气球之间的碰撞
      for (final other in balloons) {
        if (balloon != other) {
          _handleBalloonCollision(balloon, other);
        }
      }
    }
  }

  void _handleBoundaryCollision(BalloonComponent balloon) {
    const bounceFactor = 0.4;

    if (balloon.position.x < 0) {
      balloon.position.x = 0;
      balloon.velocity.x = balloon.velocity.x.abs() * bounceFactor;
    } else if (balloon.position.x > size.x - balloon.size.x) {
      balloon.position.x = size.x - balloon.size.x;
      balloon.velocity.x = -balloon.velocity.x.abs() * bounceFactor;
    }

    // 修改上边界碰撞检测，使用安全区域高度
    if (balloon.position.y < SafeZoneComponent.safeZoneHeight) {
      balloon.position.y = SafeZoneComponent.safeZoneHeight;
      balloon.velocity.y = balloon.velocity.y.abs() * bounceFactor;
    } else if (balloon.position.y > size.y - balloon.size.y) {
      balloon.position.y = size.y - balloon.size.y;
      balloon.velocity.y = -balloon.velocity.y.abs() * bounceFactor;
    }
  }

  void _handleBalloonCollision(BalloonComponent b1, BalloonComponent b2) {
    final Vector2 diff = b1.position - b2.position;
    final double distance = diff.length;
    // 调整碰撞距离为气球实际大小的一半
    final double minDistance = (b1.size.x + b2.size.x) * 0.40; // 从0.45降到0.35

    if (distance < minDistance) {
      final double overlap = minDistance - distance;
      final Vector2 separationDir = diff.normalized();

      // 立即分离气球
      final Vector2 separation = separationDir * overlap;
      b1.position += separation * 0.5;
      b2.position -= separation * 0.5;

      // 计算碰撞后的速度
      if (distance > 0) {
        final Vector2 normal = separationDir;
        final Vector2 tangent = Vector2(-normal.y, normal.x);

        final double v1n = b1.velocity.dot(normal);
        final double v1t = b1.velocity.dot(tangent);
        final double v2n = b2.velocity.dot(normal);
        final double v2t = b2.velocity.dot(tangent);

        // 计算碰撞后的速度（完全弹性碰撞）
        b1.velocity = normal * (-v1n) + tangent * v1t;
        b2.velocity = normal * (-v2n) + tangent * v2t;

        // 添加小幅随机扰动
        const double disturbance = 2.0;
        b1.velocity += Vector2(
          (random.nextDouble() - 0.5) * disturbance,
          (random.nextDouble() - 0.5) * disturbance,
        );
        b2.velocity += Vector2(
          (random.nextDouble() - 0.5) * disturbance,
          (random.nextDouble() - 0.5) * disturbance,
        );
      }
    }
  }

  @override
  void onRemove() {
    _audioController.stop();
    super.onRemove();
  }

  // 添加重播当前音标的方法
  Future<void> replayCurrentAudio() async {
    await _playCurrentPhonemeAudio(isForceListening: true);
  }

  Future<void> _loadHighScore() async {
    final prefs = await SharedPreferences.getInstance();
    gameStats.highScore = prefs.getInt('balloon_game_high_score') ?? 0;
  }

  Future<void> _saveHighScore() async {
    if (gameStats.currentScore > gameStats.highScore) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('balloon_game_high_score', gameStats.currentScore);
      gameStats.highScore = gameStats.currentScore;
    }
  }

  void _updateScore(bool isCorrect, Vector2 position) {
    if (isCorrect) {
      gameStats.correctCount++;
      gameStats.comboCount++;
      gameStats.maxCombo = max(gameStats.maxCombo, gameStats.comboCount);

      // 基础分数
      int scoreToAdd = GameConstants.correctScore;

      // Combo加分
      if (gameStats.comboCount >= 4) {
        scoreToAdd += GameConstants.comboScore4Plus;
      } else if (gameStats.comboCount == 3) {
        scoreToAdd += GameConstants.comboScore3;
      } else if (gameStats.comboCount == 2) {
        scoreToAdd += GameConstants.comboScore2;
      }

      gameStats.currentScore += scoreToAdd;

      add(ScoreAnimation(
        position: position + Vector2(GameConstants.balloonWidth / 2, 0),
        score: scoreToAdd,
        isPositive: true,
      ));
    } else {
      gameStats.wrongCount++;
      gameStats.comboCount = 0;

      // 直接扣分，不设置下限
      gameStats.currentScore += GameConstants.wrongScore;

      add(ScoreAnimation(
        position: position + Vector2(GameConstants.balloonWidth / 2, 0),
        score: GameConstants.wrongScore.abs(),
        isPositive: false,
      ));
    }
  }
}

class BalloonComponent extends PositionComponent with TapCallbacks {
  final String phoneme;
  bool isCorrect;
  Vector2 velocity = Vector2.zero();
  late Paint paint;
  late TextPaint textPaint;
  Function(bool, Vector2)? onPop;
  final List<Color> usedColors;

  BalloonComponent({
    required this.phoneme,
    required Vector2 position,
    required Vector2 size,
    required this.usedColors,
    required this.isCorrect,
    this.onPop,
  }) : super(position: position, size: size) {
    paint = Paint()
      ..color = _getUniqueColor(usedColors)
      ..style = PaintingStyle.fill;

    textPaint = TextPaint(
      style: const TextStyle(
        fontSize: 24,
        
        color: Colors.white,
        fontFamily: 'NotoSans',
        fontFamilyFallback: [
          // 'Times New Roman',
          // 'Arial Unicode MS',
          // 'Lucida Sans Unicode',
        ],
      ),
    );
  }

  Color _getUniqueColor(List<Color> usedColors) {
    final List<Color> balloonColors = [
      Colors.red.shade600,
      Colors.blue.shade600,
      Colors.green.shade600,
      Colors.purple.shade600,
      Colors.orange.shade600,
      Colors.pink.shade600,
      Colors.teal.shade600,
    ];

    // 过滤掉已使用的颜色
    final availableColors =
        balloonColors.where((color) => !usedColors.contains(color)).toList();

    if (availableColors.isEmpty) {
      // 如果所有颜色都用完了，返回随机颜色
      return balloonColors[Random().nextInt(balloonColors.length)];
    }

    final color = availableColors[Random().nextInt(availableColors.length)];
    usedColors.add(color); // 将选中的颜色添加到已使用列表
    return color;
  }

  @override
  void render(Canvas canvas) {
    canvas.save();

    // 绘制绳子
    final stringPaint = Paint()
      ..color = paint.color.withRed((paint.color.r * 0.7).toInt())
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;

    final stringPath = Path();
    final double stringStartY = size.y * 0.85;
    final double stringEndY = size.y;
    final double centerX = size.x / 2;

    stringPath.moveTo(centerX, stringStartY);

    // 绘制波浪形的绳子
    for (var i = 0; i < 3; i++) {
      stringPath.cubicTo(
        centerX - size.x * 0.08,
        stringStartY + (stringEndY - stringStartY) * (i + 0.3) / 3,
        centerX + size.x * 0.08,
        stringStartY + (stringEndY - stringStartY) * (i + 0.7) / 3,
        centerX,
        stringStartY + (stringEndY - stringStartY) * (i + 1) / 3,
      );
    }
    canvas.drawPath(stringPath, stringPaint);

    // 绘制气球
    canvas.drawOval(
      Rect.fromCenter(
        center: Offset(size.x / 2, size.y * 0.45),
        width: size.x * 0.8,
        height: size.y * 0.8,
      ),
      paint,
    );

    // 调整文字位置到气球正中心
    textPaint.render(
      canvas,
      _formatPhoneme(phoneme),
      Vector2(size.x / 2, size.y * 0.45),
      anchor: Anchor.center,
    );

    canvas.restore();
  }

  @override
  void update(double dt) {
    super.update(dt);
    position += velocity * dt;
  }

  @override
  bool onTapDown(TapDownEvent event) {
    if (isMounted) {
      final audioController = OxAudioController.to;

      // 如果是强制播放状态,只显示动画并阻止后续操作
      if (audioController.isForceListening) {
        // 显示音频播放动画
        parent?.add(
          AudioPlayingEffect(
            position: Offset(
              position.x + size.x / 2,
              position.y + size.y / 2,
            ),
          ),
        );
        // 返回 true 来消费这个事件，阻止后续操作
        return true;
      }

      // 非强制播放状态下才执行气球点击逻辑
      removeFromParent();

      if (isCorrect) {
        parent?.add(
          ParticleEffect(
            position: Offset(
              position.x + size.x / 2,
              position.y + size.y / 2,
            ),
            isCorrect: isCorrect,
            onComplete: () {},
            balloonColor: paint.color,
          ),
        );
      } else {
        parent?.add(
          WrongParticleEffect(
            position: Offset(
              position.x + size.x / 2,
              position.y + size.y / 2,
            ),
            color: paint.color,
          ),
        );
      }

      // 传递气球位置
      onPop?.call(isCorrect, Vector2(position.x, position.y));
      return true;
    }
    return false;
  }

  String _formatPhoneme(String phoneme) {
    // 不再移除斜杠，只移除方括号
    String formatted = phoneme.replaceAll(RegExp(r'[\[\]]'), '');

    // 如果没有斜杠，添加斜杠
    if (!formatted.startsWith('/')) {
      formatted = '/$formatted';
    }
    if (!formatted.endsWith('/')) {
      formatted = '$formatted/';
    }

    return formatted;
  }

  void updateCorrectStatus(bool newCorrectStatus) {
    isCorrect = newCorrectStatus;
  }
}

/// 粒子效果组件
class ParticleEffect extends Component {
  final Offset position;
  final bool isCorrect;
  final VoidCallback onComplete;
  final Color balloonColor; // 添加气球颜色
  late Timer timer;
  late List<Particle> particles;

  ParticleEffect({
    required this.position,
    required this.isCorrect,
    required this.onComplete,
    required this.balloonColor, // 接收气球颜色
  }) {
    timer = Timer(1.5, onTick: () {
      // 增加动画时长
      onComplete();
      removeFromParent();
    });
    _initializeParticles();
  }

  void _initializeParticles() {
    final random = Random();
    final particleCount = isCorrect ? 50 : 40; // 增加粒子数量

    // 创建更鲜艳的粒子颜色列表
    final List<Color> particleColors = [
      balloonColor,
      balloonColor.withValues(red: 0, green: 0, blue: 0, alpha: 0.9),
      isCorrect ? Colors.yellow.shade500 : Colors.red.shade500,
      isCorrect ? Colors.amber.shade400 : Colors.orange.shade400,
      Colors.white,
    ];

    particles = List.generate(
      particleCount,
      (_) {
        final double speed =
            (isCorrect ? 300 : 250) * (0.8 + random.nextDouble() * 0.4); // 增加速度
        final double size = random.nextDouble() * 8 + 4; // 增大粒子尺寸

        return Particle(
          position: position,
          angle: random.nextDouble() * 2 * pi,
          speed: speed,
          color: particleColors[random.nextInt(particleColors.length)],
          size: size,
          decay: 0.5 + random.nextDouble() * 0.3, // 降低衰减速度
        );
      },
    );
  }

  @override
  void update(double dt) {
    timer.update(dt);
    for (final particle in particles) {
      particle.update(dt);
    }
  }

  @override
  void render(Canvas canvas) {
    for (final particle in particles) {
      particle.render(canvas);
    }
  }
}

class Particle {
  Offset position;
  final double angle;
  final double speed;
  final Color color;
  final double size;
  final double decay;
  double life = 1.0;

  Particle({
    required this.position,
    required this.angle,
    required this.speed,
    required this.color,
    required this.size,
    required this.decay,
  });

  void update(double dt) {
    position += Offset(
      cos(angle) * speed * dt,
      sin(angle) * speed * dt + 30 * dt, // 减小重力影响
    );
    life -= dt * decay; // 使用衰减速度
  }

  void render(Canvas canvas) {
    if (life <= 0) return;
    final paint = Paint()
      ..color = color.withValues(
          red: 0, green: 0, blue: 0, alpha: life * 0.9) // 提高不透明度
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 1); // 减小模糊程度
    canvas.drawCircle(position, size * life, paint);
  }
}

// 用于存储气球位置和速度的类
class BalloonPosition {
  final Offset position;
  final Offset velocity;

  BalloonPosition({
    required this.position,
    required this.velocity,
  });

  BalloonPosition copyWith({
    Offset? position,
    Offset? velocity,
  }) {
    return BalloonPosition(
      position: position ?? this.position,
      velocity: velocity ?? this.velocity,
    );
  }
}

// 添加粒子系统组件
class ParticleSystemComponent extends Component {
  final Vector2 position;
  final List<ParticleEffect> particles;
  late Timer timer;

  ParticleSystemComponent({
    required this.position,
    required bool isCorrect,
  }) : particles = [] {
    _initializeParticles(isCorrect);
  }

  void _initializeParticles(bool isCorrect) {
    final count = isCorrect ? 20 : 30;
    final colors =
        isCorrect ? [Colors.yellow, Colors.amber] : [Colors.red, Colors.orange];

    for (int i = 0; i < count; i++) {
      add(
        ParticleEffect(
          position: Offset(position.x, position.y),
          isCorrect: isCorrect,
          onComplete: () {},
          balloonColor: colors[i],
        ),
      );
    }

    timer = Timer(1, onTick: () => removeFromParent());
  }

  @override
  void update(double dt) {
    super.update(dt);
    timer.update(dt);
    for (final particle in particles) {
      particle.update(dt);
    }
  }

  @override
  void render(Canvas canvas) {
    for (final particle in particles) {
      particle.render(canvas);
    }
  }
}

// 添加背景组件
class SkyBackground extends Component with HasGameRef {
  late Paint _skyPaint;
  late List<Cloud> _clouds;
  final Random random = Random();

  @override
  Future<void> onLoad() async {
    _skyPaint = Paint()
      ..color = const Color(0xFF87CEEB)
      ..style = PaintingStyle.fill;

    // 初始化云朵
    _clouds = List.generate(
      6,
      (index) => Cloud(
        position: Vector2(
          random.nextDouble() * gameRef.size.x,
          random.nextDouble() * gameRef.size.y * 0.6,
        ),
        scale: 0.5 + random.nextDouble() * 1.0,
      ),
    );
  }

  @override
  void render(Canvas canvas) {
    canvas.drawRect(
      Rect.fromLTWH(0, 0, gameRef.size.x, gameRef.size.y),
      _skyPaint,
    );

    for (final cloud in _clouds) {
      cloud.render(canvas);
    }
  }

  @override
  void update(double dt) {
    for (final cloud in _clouds) {
      cloud.update(dt);
      if (cloud.position.x > gameRef.size.x + cloud.width) {
        cloud.position.x = -cloud.width;
        cloud.position.y = random.nextDouble() * gameRef.size.y * 0.6;
      }
    }
  }
}

// 云朵类
class Cloud {
  Vector2 position;
  final double scale;
  final double speed;
  late Paint _paint;
  late double width;

  Cloud({
    required this.position,
    required this.scale,
  }) : speed = 10 + Random().nextDouble() * 10 {
    _paint = Paint()
      ..color =
          Colors.white.withValues(red: 255, green: 255, blue: 255, alpha: 0.8)
      ..style = PaintingStyle.fill;
    width = 100 * scale;
  }

  void render(Canvas canvas) {
    canvas.save();
    canvas.translate(position.x, position.y);
    canvas.scale(scale);

    // 绘制云朵的圆形组合
    canvas.drawCircle(const Offset(40, 0), 25, _paint);
    canvas.drawCircle(const Offset(60, 0), 30, _paint);
    canvas.drawCircle(const Offset(20, 0), 20, _paint);
    canvas.drawCircle(const Offset(80, 0), 20, _paint);

    canvas.restore();
  }

  void update(double dt) {
    position.x += speed * dt;
  }
}

class WrongParticleEffect extends Component {
  final Offset position;
  late Timer timer;
  double radius = 0.0;
  double opacity = 1.0;
  final Color color;
  static const double maxRadius = 80.0; // 增大最大半径
  static const double animationDuration = 0.8; // 增加动画时长
  double _crossScale = 0.0; // 添加X标记的缩放动画
  double _shakeOffset = 0.0; // 添加抖动效果

  WrongParticleEffect({
    required this.position,
    this.color = Colors.red,
  }) {
    timer = Timer(
      animationDuration,
      onTick: () => removeFromParent(),
    );
  }

  @override
  void update(double dt) {
    timer.update(dt);

    // 更新半径和透明度，使用缓动函数使动画更平滑
    final progress = timer.progress;
    radius = maxRadius * _easeOutBack(progress);
    opacity = 1.0 - _easeInQuad(progress);

    // 更新X标记的缩放
    _crossScale = _easeOutElastic(progress);

    // 添加抖动效果
    if (progress < 0.3) {
      _shakeOffset = sin(progress * 50) * (1 - progress / 0.3) * 5.0;
    } else {
      _shakeOffset = 0.0;
    }
  }

  @override
  void render(Canvas canvas) {
    final paint = Paint()
      ..color = color.withValues(red: 0, green: 0, blue: 0, alpha: opacity)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4.0
      ..strokeCap = StrokeCap.round; // 添加圆角效果

    canvas.save();
    canvas.translate(position.dx + _shakeOffset, position.dy);

    // 绘制外圈波纹
    canvas.drawCircle(Offset.zero, radius, paint);

    // 绘制内圈波纹
    paint.color =
        color.withValues(red: 0, green: 0, blue: 0, alpha: opacity * 0.7);
    canvas.drawCircle(Offset.zero, radius * 0.7, paint);

    // 绘制交叉的X
    paint
      ..color = color.withValues(red: 0, green: 0, blue: 0, alpha: opacity)
      ..strokeWidth = 6.0; // 加粗X标记

    final crossSize = 25.0 * _crossScale; // 使用缩放动画

    // 绘制带有发光效果的X
    paint.maskFilter = const MaskFilter.blur(BlurStyle.outer, 2);
    canvas.drawLine(
      Offset(-crossSize, -crossSize),
      Offset(crossSize, crossSize),
      paint,
    );
    canvas.drawLine(
      Offset(-crossSize, crossSize),
      Offset(crossSize, -crossSize),
      paint,
    );

    // 移除模糊效果，绘制清晰的X
    paint.maskFilter = null;
    canvas.drawLine(
      Offset(-crossSize, -crossSize),
      Offset(crossSize, crossSize),
      paint,
    );
    canvas.drawLine(
      Offset(-crossSize, crossSize),
      Offset(crossSize, -crossSize),
      paint,
    );

    canvas.restore();
  }

  // 缓动函数
  double _easeOutBack(double t) {
    const c1 = 1.70158;
    const c3 = c1 + 1;
    return 1 + c3 * pow(t - 1, 3) + c1 * pow(t - 1, 2);
  }

  double _easeInQuad(double t) {
    return t * t;
  }

  double _easeOutElastic(double t) {
    const c4 = (2 * pi) / 3;
    if (t == 0 || t == 1) return t;
    return pow(2, -10 * t) * sin((t * 10 - 0.75) * c4) + 1;
  }
}

/// 分数显示组件
class ScoreDisplay extends PositionComponent with HasGameRef {
  final GameStats gameStats;
  late TextPaint _textPaint;
  late Vector2 _screenSize;

  ScoreDisplay(this.gameStats) {
    _textPaint = TextPaint(
      style: const TextStyle(
        fontSize: 28,
        
        color: Color(0xFFFFD700), // 金色
        shadows: [
          Shadow(
            color: Colors.black54,
            offset: Offset(2, 2),
            blurRadius: 3,
          ),
        ],
      ),
    );
  }

  @override
  void onMount() {
    super.onMount();
    _screenSize = gameRef.size;
    // 计算左侧位置
    position = Vector2(
      _screenSize.x * 0.25, // 屏幕1/4处
      20,
    );
  }

  @override
  void render(Canvas canvas) {
    _textPaint.render(
      canvas,
      '分数: ${gameStats.currentScore}',
      Vector2.zero(),
      anchor: Anchor.center, // 使用中心锚点
    );
  }
}

/// 连击显示组件
class ComboDisplay extends PositionComponent with HasGameRef {
  final GameStats gameStats;
  late TextPaint _textPaint;
  late Vector2 _screenSize;

  ComboDisplay(this.gameStats) {
    _textPaint = TextPaint(
      style: const TextStyle(
        fontSize: 28,
        
        color: Color(0xFFFF4D4D), // 亮橙红色
        shadows: [
          Shadow(
            color: Colors.black54,
            offset: Offset(2, 2),
            blurRadius: 3,
          ),
        ],
      ),
    );
  }

  @override
  void onMount() {
    super.onMount();
    _screenSize = gameRef.size;
    position = Vector2(
      _screenSize.x * 0.75,
      20,
    );
  }

  @override
  void render(Canvas canvas) {
    // 移除条件判断，始终显示连击数
    _textPaint.render(
      canvas,
      '${gameStats.comboCount} 连击',
      Vector2.zero(),
      anchor: Anchor.center,
    );
  }

  @override
  void update(double dt) {
    super.update(dt);
    // 当连击数大于1时使用高亮颜色，否则使用暗色
    final targetColor = gameStats.comboCount > 1
        ? const Color(0xFFFF4D4D) // 亮橙红色
        : const Color(0xFF666666); // 暗灰色

    _textPaint = TextPaint(
      style: TextStyle(
        fontSize: 28,
        
        color: targetColor,
        shadows: const [
          Shadow(
            color: Colors.black54,
            offset: Offset(2, 2),
            blurRadius: 3,
          ),
        ],
      ),
    );
  }
}

/// 分数动画组件
class ScoreAnimation extends PositionComponent {
  final int score;
  final bool isPositive;
  double _opacity = 1.0;
  double _offsetY = 0.0;

  ScoreAnimation({
    required Vector2 position,
    required this.score,
    required this.isPositive,
  }) : super(position: position);

  @override
  void update(double dt) {
    super.update(dt);
    _opacity = max(0.0, _opacity - dt);
    _offsetY -= dt * 50;

    if (_opacity <= 0) {
      removeFromParent();
    }
  }

  @override
  void render(Canvas canvas) {
    final textPaint = TextPaint(
      style: TextStyle(
        fontSize: 28,
        
        color: (isPositive ? const Color(0xFF4CAF50) : const Color(0xFFFF4D4D))
            .withValues(red: 0, green: 0, blue: 0, alpha: _opacity),
        shadows: [
          Shadow(
            color: Colors.black54
                .withValues(red: 0, green: 0, blue: 0, alpha: _opacity),
            offset: const Offset(2, 2),
            blurRadius: 3,
          ),
        ],
      ),
    );

    textPaint.render(
      canvas,
      '${isPositive ? "+" : "-"}$score',
      Vector2(0, _offsetY),
    );
  }
}

// 添加安全区域组件
class SafeZoneComponent extends PositionComponent {
  static const double safeZoneHeight = 100.0;

  @override
  void render(Canvas canvas) {
    // 可以添加调试模式来显示安全区域
    // final paint = Paint()
    //   ..color = Colors.red.withValues(red: 0, green: 0, blue: 0, alpha: 0.2)
    //   ..style = PaintingStyle.fill;
    // canvas.drawRect(
    //   Rect.fromLTWH(0, 0, game.size.x, safeZoneHeight),
    //   paint,
    // );
  }

  @override
  bool containsPoint(Vector2 point) => point.y < safeZoneHeight;
}

/// 音频播放提示动画组件
class AudioPlayingEffect extends Component {
  final Offset position;
  late Timer timer;
  double _scale = 0.0;
  double _opacity = 0.8;
  final Color _baseColor = Colors.white;
  final Color _glowColor = const Color(0xFFE0E0E0);
  final List<Circle> _circles = [];
  static const double _maxRadius = 45.0;
  static const int _circleCount = 3;

  AudioPlayingEffect({
    required this.position,
  }) {
    timer = Timer(
      0.8,
      onTick: () => removeFromParent(),
    );

    // 初始化同心圆
    for (int i = 0; i < _circleCount; i++) {
      _circles.add(Circle(
        delay: i * 0.2,
        maxRadius: _maxRadius - (i * 8.0),
      ));
    }

    // 初始化图标绘制器
  }

  @override
  void render(Canvas canvas) {
    canvas.save();
    canvas.translate(position.dx, position.dy);
    canvas.scale(_scale);

    // 添加发光效果
    final glowPaint = Paint()
      ..color = _glowColor.withValues(
          red: 0, green: 0, blue: 0, alpha: _opacity * 0.7)
      ..maskFilter = const MaskFilter.blur(BlurStyle.outer, 10.0);

    canvas.drawCircle(
      Offset.zero,
      _maxRadius * 0.7,
      glowPaint,
    );

    // 绘制同心圆
    for (final circle in _circles) {
      final paint = Paint()
        ..color = _baseColor.withValues(
            red: 0, green: 0, blue: 0, alpha: circle.opacity * _opacity)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 4.0
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 0.5);

      canvas.drawCircle(
        Offset.zero,
        circle.radius,
        paint,
      );
    }

    // 绘制背景阴影
    final shadowPaint = Paint()
      ..color = Colors.black
          .withValues(red: 0, green: 0, blue: 0, alpha: _opacity * 0.2)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3.0);

    canvas.drawCircle(
      const Offset(2, 2),
      _maxRadius * 0.45,
      shadowPaint,
    );

    // 绘制白色背景圆
    final bgPaint = Paint()
      ..color =
          _baseColor.withValues(red: 0, green: 0, blue: 0, alpha: _opacity)
      ..style = PaintingStyle.fill;

    canvas.drawCircle(
      Offset.zero,
      _maxRadius * 0.45,
      bgPaint,
    );

    // 绘制图标阴影
    final iconShadowPainter = TextPainter(
      text: TextSpan(
        text: String.fromCharCode(Icons.music_note.codePoint),
        style: TextStyle(
          fontSize: 28.0,
          fontFamily: 'MaterialIcons',
          color: Colors.black
              .withValues(red: 0, green: 0, blue: 0, alpha: _opacity * 0.3),
          shadows: const [
            Shadow(
              color: Colors.black26,
              offset: Offset(1, 1),
              blurRadius: 2,
            ),
          ],
        ),
      ),
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    )..layout();

    // 计算居中偏移量
    final iconOffset = Offset(
      -iconShadowPainter.width / 2,
      -iconShadowPainter.height / 2,
    );

    // 绘制阴影和主图标
    iconShadowPainter.paint(canvas, iconOffset);

    // 绘制主图标
    final mainIconPainter = TextPainter(
      text: TextSpan(
        text: String.fromCharCode(Icons.music_note.codePoint),
        style: TextStyle(
          fontSize: 28.0,
          fontFamily: 'MaterialIcons',
          color: Colors.black
              .withValues(red: 0, green: 0, blue: 0, alpha: _opacity * 0.8),
        ),
      ),
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    )..layout();

    mainIconPainter.paint(canvas, iconOffset);
    canvas.restore();
  }

  @override
  void update(double dt) {
    timer.update(dt);

    // 更新所有圆的状态
    for (final circle in _circles) {
      circle.update(dt);
    }

    // 整体缩放动画
    _scale = _easeOutBack(min(1.0, timer.progress * 2));
    _opacity = 1.0 - _easeInQuad(timer.progress);
  }

  double _easeOutBack(double t) {
    const c1 = 1.70158;
    const c3 = c1 + 1;
    return 1 + c3 * pow(t - 1, 3) + c1 * pow(t - 1, 2);
  }

  double _easeInQuad(double t) {
    return t * t;
  }
}

/// 辅助类：用于管理单个圆的动画状态
class Circle {
  double radius = 0.0;
  double opacity = 0.0;
  final double delay;
  final double maxRadius;
  double _progress = 0.0;

  Circle({
    required this.delay,
    required this.maxRadius,
  });

  void update(double dt) {
    if (_progress < delay) {
      _progress += dt;
      return;
    }

    final actualProgress = (_progress - delay) / 0.6; // 0.6秒完成一次动画
    if (actualProgress <= 1.0) {
      radius = maxRadius * _easeOutCubic(actualProgress);
      opacity = 1.0 - actualProgress;
    }
    _progress += dt;
  }

  double _easeOutCubic(double t) {
    return 1 - pow(1 - t, 3).toDouble();
  }
}
