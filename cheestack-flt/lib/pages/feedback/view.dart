part of feedback;

// FeedbackType enum
// ignore: constant_identifier_names
enum FeedbackType { violation, suggestion, other, incorrect_content }

// extendend
// 扩展`FeedbackType`枚举类，返回对应的`值,
// 例如：`FeedbackType.suggestion`返回`suggestion`
extension CardTypeExtension on FeedbackType {
  String get value => toString().split('.').last.toSnakeCase;
}

class FeedbackPage extends GetView<FeedbackController> {
  const FeedbackPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<FeedbackController>(
      init: FeedbackController(),
      builder: (_) {
        return Scaffold(
          appBar: AppBar(title: Text(LocaleKeys.settingsHelpFeedback.tr)),
          body: Safe<PERSON><PERSON>(
            child: _buildView(),
          ),
        );
      },
    );
  }

  Widget _buildView() {
    return Container(
      margin: EdgeInsets.all(AppTheme.margin),
      child: Column(
        children: [
          Form(
              key: controller.form<PERSON><PERSON>,
              child: <PERSON>um<PERSON>(
                children: [
                  _buildFeedbackType(),
                  const <PERSON><PERSON><PERSON><PERSON>(height: 16),
                  Ox<PERSON><PERSON><PERSON><PERSON>(
                    label: "意见或建议",
                    controller: controller.contentCtl,
                    maxLines: 5,
                    hintText: "请输入内容",
                    validator: RequiredValidator(),
                  )
                ],
              )),
          const SizedBox(height: 16),
          _buildResutlButton(),
        ],
      ),
    );
  }

  Widget _buildResutlButton() {
    return Sbutton(
        width: double.infinity,
        onPressed: controller.onSubmit,
        child: const Text('提交'));
  }

  Widget _buildFeedbackType() {
    return DropdownButtonFormField(
      value: FeedbackType.incorrect_content.value,
      items: [
        DropdownMenuItem<String>(
          value: FeedbackType.incorrect_content.value,
          child: const Text("内容错误"),
        ),
        DropdownMenuItem<String>(
          value: FeedbackType.suggestion.value,
          child: const Text("内容建议"),
        ),
        DropdownMenuItem<String>(
          value: FeedbackType.violation.value,
          child: const Text("举报违规"),
        ),
        DropdownMenuItem<String>(
          value: FeedbackType.other.value,
          child: const Text("其他问题"),
        ),
      ],
      onChanged: (Object? value) {
        controller.type = value as String;
      },
    );
  }
}
