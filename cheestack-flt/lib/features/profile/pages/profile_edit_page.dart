import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:cheestack_flt/widgets/index.dart';
import '../controllers/profile_edit_controller.dart';

/// 用户信息编辑页面
///
/// 全新设计的编辑页面，使用专门的ProfileEditController
/// 完全独立的状态管理，避免与其他组件的冲突
class ProfileEditPage extends StatelessWidget {
  const ProfileEditPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProfileEditController>(
      init: ProfileEditController(),
      builder: (controller) {
        return Scaffold(
          appBar: _buildAppBar(controller),
          body: _buildBody(controller),
        );
      },
    );
  }

  /// 构建应用栏
  PreferredSizeWidget _buildAppBar(ProfileEditController controller) {
    return AppBar(
      title: const Text('编辑资料'),
      leading: IconButton(
        icon: const Icon(Icons.close),
        onPressed: controller.cancelEdit,
      ),
      actions: [
        Obx(() => TextButton(
              onPressed: controller.hasChanges && !controller.isLoading
                  ? controller.saveProfile
                  : null,
              child: controller.isLoading
                  ? SizedBox(
                      width: 20.w,
                      height: 20.w,
                      child: const CircularProgressIndicator(strokeWidth: 2),
                    )
                  : Text(
                      '保存',
                      style: TextStyle(
                        color: controller.hasChanges
                            ? Get.theme.colorScheme.primary
                            : Get.theme.colorScheme.onSurface
                                .withValues(alpha: 0.38),
                        
                      ),
                    ),
            )),
      ],
    );
  }

  /// 构建页面主体
  Widget _buildBody(ProfileEditController controller) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(24.w),
      child: Form(
        key: controller.formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // 头像编辑区域
            _buildAvatarSection(controller),

            SizedBox(height: 40.h),

            // 表单字段
            _buildFormFields(controller),

            SizedBox(height: 40.h),

            // 提示信息
            _buildHintText(),
          ],
        ),
      ),
    );
  }

  /// 构建头像编辑区域
  Widget _buildAvatarSection(ProfileEditController controller) {
    return Obx(() => Column(
          children: [
            // 头像容器
            GestureDetector(
              onTap: controller.previewAvatar,
              child: Container(
                width: 120.w,
                height: 120.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Get.theme.colorScheme.outline.withValues(alpha: 0.3),
                    width: 2,
                  ),
                ),
                child: ClipOval(
                  child: controller.hasTempAvatar
                      ? Image.file(
                          controller.tempAvatarFile!,
                          fit: BoxFit.cover,
                        )
                      : controller.currentAvatarUrl.isNotEmpty
                          ? Simage(
                              url: controller.currentAvatarUrl,
                              fit: BoxFit.cover,
                            )
                          : Container(
                              color:
                                  Get.theme.colorScheme.surfaceContainerHighest,
                              child: Icon(
                                Icons.person,
                                size: 60.sp,
                                
                              ),
                            ),
                ),
              ),
            ),

            SizedBox(height: 16.h),

            // 头像操作按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildAvatarButton(
                  icon: Icons.photo_library_outlined,
                  label: '相册',
                  onTap: controller.selectAvatar,
                ),
                SizedBox(width: 24.w),
                _buildAvatarButton(
                  icon: Icons.camera_alt_outlined,
                  label: '拍照',
                  onTap: controller.takePhoto,
                ),
                if (controller.hasTempAvatar) ...[
                  SizedBox(width: 24.w),
                  _buildAvatarButton(
                    icon: Icons.delete_outline,
                    label: '删除',
                    onTap: controller.clearTempAvatar,
                    isDestructive: true,
                  ),
                ],
              ],
            ),

            SizedBox(height: 8.h),

            // 头像提示
            Text(
              '点击头像预览，使用按钮更换',
              style: TextStyle(
                fontSize: 14.sp,
                color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
          ],
        ));
  }

  /// 构建头像操作按钮
  Widget _buildAvatarButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 48.w,
            height: 48.w,
            decoration: BoxDecoration(
              color: isDestructive
                  ? Get.theme.colorScheme.errorContainer
                  : Get.theme.colorScheme.primaryContainer,
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              size: 24.sp,
              color: isDestructive
                  ? Get.theme.colorScheme.onErrorContainer
                  : Get.theme.colorScheme.onPrimaryContainer,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            label,
            style: TextStyle(
              fontSize: 12.sp,
              color: isDestructive
                  ? Get.theme.colorScheme.error
                  : Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建表单字段
  Widget _buildFormFields(ProfileEditController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 用户名字段
        _buildInputField(
          label: '用户名',
          controller: controller.usernameController,
          hintText: '请输入用户名',
          icon: Icons.person_outline,
          validator: controller.validateUsername,
        ),

        SizedBox(height: 24.h),

        // 个人简介字段
        _buildInputField(
          label: '个人简介',
          controller: controller.introController,
          hintText: '介绍一下自己吧',
          icon: Icons.edit_note_outlined,
          maxLines: 4,
          validator: controller.validateIntro,
        ),
      ],
    );
  }

  /// 构建输入字段
  Widget _buildInputField({
    required String label,
    required TextEditingController controller,
    required String hintText,
    required IconData icon,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标签
        Row(
          children: [
            Icon(
              icon,
              size: 20.sp,
              color: Get.theme.colorScheme.primary,
            ),
            SizedBox(width: 8.w),
            Text(
              label,
              style: TextStyle(
                fontSize: 16.sp,
                
                color: Get.theme.colorScheme.onSurface,
              ),
            ),
          ],
        ),

        SizedBox(height: 12.h),

        // 输入框
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          validator: validator,
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: TextStyle(
              color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
              borderSide: BorderSide(
                color: Get.theme.colorScheme.outline,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
              borderSide: BorderSide(
                color: Get.theme.colorScheme.outline.withValues(alpha: 0.5),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
              borderSide: BorderSide(
                color: Get.theme.colorScheme.primary,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
              borderSide: BorderSide(
                color: Get.theme.colorScheme.error,
              ),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
              borderSide: BorderSide(
                color: Get.theme.colorScheme.error,
                width: 2,
              ),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: 16.w,
              vertical: 16.h,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建提示信息
  Widget _buildHintText() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Get.theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            size: 20.sp,
            color: Get.theme.colorScheme.primary,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Text(
              '修改后的信息将在保存后生效',
              style: TextStyle(
                fontSize: 14.sp,
                color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
