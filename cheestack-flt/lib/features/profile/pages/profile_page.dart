import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:cheestack_flt/features/auth/controllers/controller.dart';
import 'package:cheestack_flt/routes/index.dart';
import 'package:cheestack_flt/common/enums/index.dart';
import 'package:cheestack_flt/i18n/index.dart';
import '../controllers/profile_controller.dart';
import '../widgets/unified_option_item.dart';
import 'widgets/widgets.dart';

/// Profile 页面
/// 用户配置文件主页面
class ProfilePage extends StatefulWidget {
  const ProfilePage({Key? key}) : super(key: key);

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return GetBuilder<ProfileController>(
      init: ProfileController(),
      builder: (controller) {
        return Scaffold(
          appBar: _buildAppBar(controller),
          body: _buildView(controller),
        );
      },
    );
  }

  /// 构建应用栏
  OxAppBar _buildAppBar(ProfileController controller) {
    return OxAppBar(
      title: Text(controller.isEditMode
          ? LocaleKeys.profileEditTitle.tr
          : LocaleKeys.profileTitle.tr ?? ""),
      leading: controller.isEditMode
          ? IconButton(
              icon: const Icon(Icons.arrow_back_ios),
              onPressed: controller.exitEditMode,
            )
          : null,
    );
  }

  /// 构建主视图
  Widget _buildView(ProfileController controller) {
    return SingleChildScrollView(
      child: SafeArea(
        minimum: REdgeInsets.all(AppTheme.paddingMedium.left),
        child: GetBuilder<AuthController>(
          init: AuthController(),
          builder: (_) {
            // 始终显示查看模式，编辑通过路由导航
            return Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                // 用户信息区域
                _buildUserInfo(),
                SizedBox(height: 20.h),

                // 应用设置区域
                _buildAppSettings(),
                SizedBox(height: 20.h),

                // 账户管理区域
                _buildAccountManagement(),
                SizedBox(height: 20.h),

                // 其他功能区域
                _buildOtherFeatures(),
                SizedBox(height: 20.h),
              ],
            );
          },
        ),
      ),
    );
  }

  /// 构建用户信息区域
  Widget _buildUserInfo() {
    return GetBuilder<ProfileController>(
      init: ProfileController(),
      builder: (controller) {
        return GetBuilder<AuthController>(
          init: AuthController(),
          builder: (_) {
            return Container(
              margin: EdgeInsets.symmetric(horizontal: 4.w),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Theme.of(context).colorScheme.primaryContainer,
                    Theme.of(context).colorScheme.surfaceContainerLow,
                  ],
                ),
                borderRadius: BorderRadius.circular(24.r),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context)
                        .colorScheme
                        .shadow
                        .withValues(alpha: 0.08),
                    blurRadius: 16.r,
                    offset: Offset(0, 4.h),
                  ),
                ],
              ),
              child: Container(
                padding: EdgeInsets.all(24.w),
                child: Column(
                  children: [
                    // 头像和基本信息行
                    Row(
                      children: [
                        // 用户头像容器
                        GestureDetector(
                          onTap: () => _previewAvatar(),
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16.r),
                              boxShadow: [
                                BoxShadow(
                                  color: Theme.of(context)
                                      .colorScheme
                                      .primary
                                      .withValues(alpha: 0.2),
                                  blurRadius: 12.r,
                                  offset: Offset(0, 4.h),
                                ),
                              ],
                            ),
                            child: Container(
                              width: 72.w,
                              height: 72.w,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16.r),
                                border: Border.all(
                                  color: Theme.of(context)
                                      .colorScheme
                                      .primary
                                      .withValues(alpha: 0.3),
                                  width: 2.w,
                                ),
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(14.r),
                                child: AuthController
                                            .to.usr.user?.avatar?.isNotEmpty ==
                                        true
                                    ? Image.network(
                                        AuthController.to.usr.user!.avatar!,
                                        width: 84.w,
                                        height: 84.w,
                                        fit: BoxFit.cover,
                                        errorBuilder:
                                            (context, error, stackTrace) {
                                          return Container(
                                            decoration: BoxDecoration(
                                              gradient: LinearGradient(
                                                begin: Alignment.topLeft,
                                                end: Alignment.bottomRight,
                                                colors: [
                                                  Theme.of(context)
                                                      .colorScheme
                                                      .primary,
                                                  Theme.of(context)
                                                      .colorScheme
                                                      .secondary,
                                                ],
                                              ),
                                            ),
                                            child: Center(
                                              child: Text(
                                                (AuthController
                                                            .to
                                                            .usr
                                                            .user
                                                            ?.username
                                                            ?.isNotEmpty ==
                                                        true
                                                    ? AuthController
                                                        .to.usr.user!.username!
                                                        .substring(0, 1)
                                                        .toUpperCase()
                                                    : 'U'),
                                                fontSize: 28.sp,
                                                
                                                color: Theme.of(context)
                                                    .colorScheme
                                                    .onPrimary,
                                              ),
                                            ),
                                          );
                                        },
                                      )
                                    : Container(
                                        decoration: BoxDecoration(
                                          gradient: LinearGradient(
                                            begin: Alignment.topLeft,
                                            end: Alignment.bottomRight,
                                            colors: [
                                              Theme.of(context)
                                                  .colorScheme
                                                  .primary,
                                              Theme.of(context)
                                                  .colorScheme
                                                  .secondary,
                                            ],
                                          ),
                                        ),
                                        child: Center(
                                          child: Text(
                                            (AuthController
                                                        .to
                                                        .usr
                                                        .user
                                                        ?.username
                                                        ?.isNotEmpty ==
                                                    true
                                                ? AuthController
                                                    .to.usr.user!.username!
                                                    .substring(0, 1)
                                                    .toUpperCase()
                                                : 'U'),
                                            fontSize: 28.sp,
                                            
                                            color: Theme.of(context)
                                                .colorScheme
                                                .onPrimary,
                                          ),
                                        ),
                                      ),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(width: 20.w),
                        // 用户信息
                        Expanded(
                          child: GetBuilder<ProfileController>(
                            id: 'userInfo',
                            builder: (controller) {
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // 用户名 - 滚动显示
                                  Container(
                                    height: 24.h,
                                    child: SingleChildScrollView(
                                      scrollDirection: Axis.horizontal,
                                      child: Text(AuthController.to.usr.user?.username
                                                ?.capitalize ??
                                            LocaleKeys.authUsername.tr, style: TextStyle(fontSize: 20.sp, color: Theme.of(context).colorScheme.onSurface),
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: 6.h),
                                  // 联系方式和编辑按钮行
                                  Row(
                                    children: [
                                      // 联系方式
                                      if (AuthController.to.usr.user?.mobile !=
                                          null) ...[
                                        Icon(
                                          Icons.phone_outlined,
                                          size: 14.w,
                                          color: Theme.of(context)
                                              .colorScheme
                                              .onSurfaceVariant,
                                        ),
                                        SizedBox(width: 6.w),
                                        Text(
                                          AuthController.to.usr.user!.mobile!,
                                          style: TextStyle(
                                                  fontSize: 14.sp,
                                                  color: Theme.of(context).colorScheme.onSurfaceVariant),
                                        ),
                                      ],
                                      Spacer(),
                                      // 编辑按钮
                                      Container(
                                        width: 32.w,
                                        height: 32.w,
                                        decoration: BoxDecoration(
                                          color: Theme.of(context)
                                              .colorScheme
                                              .primary
                                              .withValues(alpha: 0.1),
                                          borderRadius:
                                              BorderRadius.circular(16.r),
                                          border: Border.all(
                                            color: Theme.of(context)
                                                .colorScheme
                                                .primary
                                                .withValues(alpha: 0.2),
                                            width: 1.w,
                                          ),
                                        ),
                                        child: Material(
                                          color: Colors.transparent,
                                          child: InkWell(
                                            onTap: controller.toProfileEdit,
                                            borderRadius:
                                                BorderRadius.circular(16.r),
                                            child: Center(
                                              child: Icon(
                                                Icons.edit_outlined,
                                                size: 16.w,
                                                color: Theme.of(context)
                                                    .colorScheme
                                                    .primary,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 4.h),
                                  // 个人简介
                                  Text(
                                    AuthController.to.usr.user?.intro ??
                                        LocaleKeys.profileUserInfo.tr,
                                    style: TextStyle(
                                            fontSize: 12.sp,
                                            color: Theme.of(context).colorScheme.onSurfaceVariant),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// 构建账户管理区域
  Widget _buildAccountManagement() {
    return GetBuilder<ProfileController>(
      builder: (controller) {
        return GetBuilder<AuthController>(
          builder: (_) {
            return Column(
              children: [
                _buildSectionHeader(LocaleKeys.profileAccountManagement.tr,
                    Icons.account_circle_outlined),
                SizedBox(height: 12.h),

                // 多账户切换（如果有多个账户）
                if (AuthController.to.usernames.isNotEmpty)
                  Scell(
                    icon: const Icon(Icons.switch_account_outlined),
                    title: Text(LocaleKeys.accountSwitchAccount.tr),
                    value: Text(
                      '${AuthController.to.usernames.length}${LocaleKeys.accountsCount.tr}',
        
                          .colorScheme
                          .onSurfaceVariant,
                    ),
                    showArrow: true,
                    onTap: () => _showAccountSwitchDialog(controller),
                  ),

                // 添加新账户
                Scell(
                  icon: const Icon(Icons.person_add_outlined),
                  title: Text(LocaleKeys.accountAddAccount.tr),
                  showArrow: true,
                  onTap: () =>
                      Get.toNamed(AppRoutes.auth, arguments: AuthType.login),
                ),

                Scell(
                  icon: const Icon(Icons.security_outlined),
                  title: Text(LocaleKeys.accountSecurity.tr),
                  showArrow: true,
                  onTap: () {
                    _showSecurityOptions(controller);
                  },
                ),
                Scell(
                  icon: const Icon(Icons.logout_outlined),
                  title: Text(LocaleKeys.authLogout.tr),
                  showArrow: true,
                  onTap: () => _showLogoutConfirmDialog(),
                ),
              ],
            );
          },
        );
      },
    );
  }

  /// 构建应用设置区域
  Widget _buildAppSettings() {
    return Column(
      children: [
        _buildSectionHeader(
            LocaleKeys.profileAppSettings.tr, Icons.settings_outlined),
        SizedBox(height: 12.h),
        GetBuilder<ProfileController>(
          builder: (controller) {
            return Scell(
              icon: const Icon(Icons.palette_outlined),
              title: Text(LocaleKeys.themeSettings.tr),
              value: Text(
                controller.getCurrentThemeName(),
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              showArrow: true,
              onTap: controller.showThemeSettingDialog,
            );
          },
        ),
        GetBuilder<ProfileController>(
          builder: (controller) {
            return Scell(
              icon: const Icon(Icons.language_outlined),
              title: Text(LocaleKeys.languageSettings.tr),
              value: Text(
                controller.getCurrentLanguageName(),
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              showArrow: true,
              onTap: controller.showLanguageSettingDialog,
            );
          },
        ),
      ],
    );
  }

  /// 构建其他功能区域
  Widget _buildOtherFeatures() {
    return Column(
      children: [
        _buildSectionHeader(
            LocaleKeys.profileOtherFeatures.tr, Icons.more_horiz_outlined),
        SizedBox(height: 12.h),
        Scell(
          icon: const Icon(Icons.info_outline),
          title: Text(LocaleKeys.settingsAboutUs.tr),
          showArrow: true,
          onTap: () {
            Get.toNamed(AppRoutes.aboutUs);
          },
        ),
      ],
    );
  }

  /// 构建区域标题
  Widget _buildSectionHeader(String title, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20.w,
          color: Theme.of(context).colorScheme.primary,
        ),
        SizedBox(width: 8.w),
        Text(
          title,
          
          
        ),
      ],
    );
  }

  /// 显示账户切换底部弹窗
  void _showAccountSwitchDialog(ProfileController controller) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildUnifiedBottomSheet(
        child: Column(
          children: [
            // 标题和描述
            _buildBottomSheetHeader(
              icon: Icons.switch_account_outlined,
              title: LocaleKeys.accountSwitchTitle.tr,
              subtitle: LocaleKeys.accountSwitchDesc.tr,
            ),
            SizedBox(height: 32.h),

            // 账户列表
            ...AuthController.to.usernames.map((username) {
              final isCurrentUser =
                  username == AuthController.to.usr.user?.username;
              return UnifiedOptionItem(
                icon: Icons.account_circle,
                title: username,
                subtitle: isCurrentUser ? '当前登录账户' : '点击切换到此账户',
                isCurrentUser: isCurrentUser,
                onTap: isCurrentUser
                    ? null
                    : () async {
                        Navigator.of(context).pop();
                        await AuthController.to.switchUser(username);
                        ShowToast.success(
                            '${LocaleKeys.accountLogoutSuccess.tr}: $username');
                      },
              );
            }).toList(),

            SizedBox(height: 8.h),

            // 取消按钮
            SizedBox(
              width: double.infinity,
              child: Sbutton(
                backgroundColor:
                    Theme.of(context).colorScheme.surfaceContainerHighest,
                foregroundColor: Theme.of(context).colorScheme.onSurfaceVariant,
                onPressed: () => Navigator.of(context).pop(),
                child: Text(
                  LocaleKeys.cancel.tr,
                  
                ),
              ),
            ),
            SizedBox(height: 16.h),
          ],
        ),
      ),
    );
  }

  /// 显示退出登录确认底部弹窗
  void _showLogoutConfirmDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildUnifiedBottomSheet(
        child: Column(
          children: [
            // 标题和描述
            _buildBottomSheetHeader(
              icon: Icons.logout_outlined,
              title: LocaleKeys.authLogout.tr,
              subtitle: LocaleKeys.accountLogoutConfirm.tr,
              iconColor: Theme.of(context).colorScheme.error,
            ),
            SizedBox(height: 32.h),

            // 确认按钮
            SizedBox(
              width: double.infinity,
              child: Sbutton(
                backgroundColor: Theme.of(context).colorScheme.error,
                foregroundColor: Theme.of(context).colorScheme.onError,
                onPressed: () async {
                  Navigator.of(context).pop();
                  await AuthController.to.logout();
                  ShowToast.success(LocaleKeys.accountLogoutSuccess.tr);
                },
                child: Text(
                  LocaleKeys.authLogout.tr,
                  
                  
                ),
              ),
            ),
            SizedBox(height: 12.h),

            // 取消按钮
            SizedBox(
              width: double.infinity,
              child: Sbutton(
                backgroundColor:
                    Theme.of(context).colorScheme.surfaceContainerHighest,
                foregroundColor: Theme.of(context).colorScheme.onSurfaceVariant,
                onPressed: () => Navigator.of(context).pop(),
                child: Text(
                  LocaleKeys.cancel.tr,
                  
                ),
              ),
            ),
            SizedBox(height: 16.h),
          ],
        ),
      ),
    );
  }

  /// 构建复习数量设置（已注释，保留以备后用）
  // Widget _buildReviewNumView() {
  //   return GetBuilder<ProfileController>(
  //     init: ProfileController(),
  //     builder: (controller) {
  //       return Scell(
  //         title: Stext("单次复习数量", fontSize: AppSizes.fontTitle),
  //         onTap: controller.onChangeReviewNumber,
  //         showArrow: true,
  //         value: Text(
  //           AuthController.to.usr.config?.reviewNumber.toString( ?? "") ?? "",
  //           style: TextStyle(
  //             color: Theme.of(Get.context!).colorScheme.onSurfaceVariant,
  //             fontSize: AppSizes.fontTitle,
  //           ),
  //         ),
  //       );
  //     },
  //   );
  // }

  /// 构建学习数量设置（已注释，保留以备后用）
  // Widget _buildStudyNumView() {
  //   return GetBuilder<ProfileController>(
  //     init: ProfileController(),
  //     builder: (controller) {
  //       return GetBuilder<AuthController>(
  //         init: AuthController(),
  //         builder: (_) {
  //           return Scell(
  //             title: Stext("单次新学数量", fontSize: AppSizes.fontTitle),
  //             onTap: controller.onChangeStudyNumber,
  //             showArrow: true,
  //             value: Text(
  //               AuthController.to.usr.config?.studyNumber.toString( ?? "") ?? "",
  //               style: TextStyle(
  //                 color: Theme.of(Get.context!).colorScheme.onSurfaceVariant,
  //                 fontSize: AppSizes.fontTitle,
  //               ),
  //             ),
  //           );
  //         },
  //       );
  //     },
  //   );
  // }

  /// 构建音频播放开关（已注释，保留以备后用）
  // Widget _buildAudioPlaySwitch() {
  //   return GetBuilder<ProfileController>(
  //     init: ProfileController(),
  //     builder: (controller) {
  //       return Scell(
  //         title: Stext("自动播放音频", fontSize: AppSizes.fontTitle),
  //         showArrow: false,
  //         value: CustomSwitch(
  //           value: AuthController.to.usr.config?.isAutoPlayAudio ?? false,
  //           onChanged: (value) {
  //             controller.onChangeAutoPlay(value);
  //           },
  //         ),
  //       );
  //     },
  //   );
  // }

  /// 显示安全选项
  void _showSecurityOptions(ProfileController controller) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildUnifiedBottomSheet(
        child: Column(
          children: [
            // 标题和描述
            _buildBottomSheetHeader(
              icon: Icons.security_outlined,
              title: LocaleKeys.accountSecurity.tr,
              subtitle: '管理您的账户安全设置',
            ),
            SizedBox(height: 24.h),

            // 修改密码选项
            UnifiedOptionItem(
              icon: Icons.lock_outline,
              title: LocaleKeys.securityChangePassword.tr,
              subtitle: '修改登录密码',
              showArrow: true,
              onTap: () {
                Navigator.of(context).pop();
                Get.to(() => const ChangePasswordPage());
              },
            ),

            SizedBox(height: 16.h),

            // 删除账户选项
            UnifiedOptionItem(
              icon: Icons.delete_forever_outlined,
              title: '删除账户',
              subtitle: '永久删除您的账户和所有数据',
              iconColor: Theme.of(context).colorScheme.error,
              showArrow: true,
              onTap: () {
                Navigator.of(context).pop();
                controller.showDeleteAccountDialog();
              },
            ),

            SizedBox(height: 16.h),

            // 取消按钮
            SizedBox(
              width: double.infinity,
              child: Sbutton(
                backgroundColor:
                    Theme.of(context).colorScheme.surfaceContainerHighest,
                foregroundColor: Theme.of(context).colorScheme.onSurfaceVariant,
                onPressed: () => Navigator.of(context).pop(),
                child: Text(
                  LocaleKeys.cancel.tr,
                  
                ),
              ),
            ),
            SizedBox(height: 16.h),
          ],
        ),
      ),
    );
  }

  /// 预览头像
  void _previewAvatar() {
    final avatarUrl = AuthController.to.usr.user?.avatar;
    if (avatarUrl?.isNotEmpty == true) {
      CustomGallery.show(
        context: context,
        items: [
          CustomGalleryItem(
            type: SimageType.network,
            url: avatarUrl!,
          ),
        ],
      );
    }
  }

  /// 统一的底部弹窗构建方法
  Widget _buildUnifiedBottomSheet({
    required Widget child,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(24.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 顶部指示器
              Container(
                width: 40.w,
                height: 4.h,
                decoration: BoxDecoration(
                  color: Theme.of(context)
                      .colorScheme
                      .onSurfaceVariant
                      .withValues(alpha: 0.4),
                  borderRadius: BorderRadius.circular(2.r),
                ),
              ),
              SizedBox(height: 24.h),
              child,
            ],
          ),
        ),
      ),
    );
  }

  /// 统一的弹窗标题构建方法
  Widget _buildBottomSheetHeader({
    required IconData icon,
    required String title,
    String? subtitle,
    Color? iconColor,
  }) {
    return Column(
      children: [
        // 标题行
        Row(
          children: [
            Icon(
              icon,
              size: 28.w,
              color: iconColor ?? Theme.of(context).colorScheme.primary,
            ),
            SizedBox(width: 12.w),
            Text(
              title,
              
              
            ),
          ],
        ),
        if (subtitle != null) ...[
          SizedBox(height: 8.h),
          // 描述文字
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              subtitle,

                  .colorScheme
                  .onSurfaceVariant,
            ),
          ),
        ],
      ],
    );
  }
}
