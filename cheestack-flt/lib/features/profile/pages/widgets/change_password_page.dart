import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:cheestack_flt/widgets/index.dart';
import 'package:cheestack_flt/theme.dart';
import 'package:cheestack_flt/i18n/index.dart';
import '../../controllers/profile_controller.dart';

/// 修改密码页面
/// 提供安全的密码修改功能，包含密码强度检测和要求验证
class ChangePasswordPage extends StatelessWidget {
  const ChangePasswordPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProfileController>(
      builder: (controller) {
        return Scaffold(
          appBar: _buildAppBar(),
          body: _buildBody(controller),
          bottomNavigationBar: _buildBottomButton(controller),
        );
      },
    );
  }

  /// 构建应用栏
  OxAppBar _buildAppBar() {
    return OxAppBar(
      title: Text(LocaleKeys.securityChangePassword.tr),
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_ios),
        onPressed: () => Get.back(),
      ),
    );
  }

  /// 构建主体内容
  Widget _buildBody(ProfileController controller) {
    return SingleChildScrollView(
      child: SafeArea(
        minimum: AppTheme.paddingMedium,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 当前密码输入
            _buildCurrentPasswordField(controller),
            SizedBox(height: 16.h),

            // 新密码输入
            _buildNewPasswordField(controller),
            SizedBox(height: 8.h),

            // 密码强度指示器（简化版）
            _buildSimplePasswordStrengthIndicator(controller),
            SizedBox(height: 16.h),

            // 确认密码输入
            _buildConfirmPasswordField(controller),
            SizedBox(height: 24.h),
          ],
        ),
      ),
    );
  }

  /// 构建简化的密码强度指示器
  Widget _buildSimplePasswordStrengthIndicator(ProfileController controller) {
    if (controller.newPasswordController.text.isEmpty) {
      return const SizedBox.shrink();
    }

    final strength = controller.passwordStrength;

    String strengthText;
    Color strengthColor;

    switch (strength) {
      case PasswordStrength.weak:
        strengthText = '弱';
        strengthColor = Colors.red;
        break;
      case PasswordStrength.medium:
        strengthText = '中';
        strengthColor = Colors.orange;
        break;
      case PasswordStrength.strong:
        strengthText = '强';
        strengthColor = Colors.green;
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: strengthColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6.r),
        border: Border.all(color: strengthColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '密码强度: ',
            style: TextStyle(fontSize: 12.sp, color: Colors.grey[600]),
          ),
          Text(
            strengthText,
            style: TextStyle(
              fontSize: 12.sp,
              color: strengthColor,
              
            ),
          ),
        ],
      ),
    );
  }

  /// 构建当前密码输入框
  Widget _buildCurrentPasswordField(ProfileController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleKeys.securityCurrentPassword.tr,
          
          
        ),
        SizedBox(height: 8.h),
        OxInputField(
          controller: controller.currentPasswordController,
          hintText: LocaleKeys.securityCurrentPassword.tr,
          obscureText: true,
          iconData: Icons.lock_outline,
          keyboardType: TextInputType.visiblePassword,
        ),
      ],
    );
  }

  /// 构建新密码输入框
  Widget _buildNewPasswordField(ProfileController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleKeys.securityNewPassword.tr,
          
          
        ),
        SizedBox(height: 8.h),
        OxInputField(
          controller: controller.newPasswordController,
          hintText: LocaleKeys.securityNewPassword.tr,
          obscureText: true,
          iconData: Icons.lock_outline,
          keyboardType: TextInputType.visiblePassword,
        ),
      ],
    );
  }

  /// 构建确认密码输入框
  Widget _buildConfirmPasswordField(ProfileController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleKeys.securityConfirmPassword.tr,
          
          
        ),
        SizedBox(height: 8.h),
        OxInputField(
          controller: controller.confirmPasswordController,
          hintText: LocaleKeys.securityConfirmPassword.tr,
          obscureText: true,
          iconData: Icons.lock_outline,
          keyboardType: TextInputType.visiblePassword,
        ),
      ],
    );
  }

  /// 构建底部按钮
  Widget _buildBottomButton(ProfileController controller) {
    return SafeArea(
      child: Padding(
        padding: AppTheme.paddingMedium,
        child: Sbutton(
          onPressed: controller.changePassword,
          child: Text(
            LocaleKeys.securityChangePassword.tr,
            style:
                TextStyle(color: Theme.of(Get.context!)).colorScheme.onPrimary,
          ),
        ),
      ),
    );
  }
}
