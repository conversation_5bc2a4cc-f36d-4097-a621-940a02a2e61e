import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:extended_image/extended_image.dart';
import 'package:path_provider/path_provider.dart';
import 'package:image/image.dart' as img;
import 'package:cheestack_flt/shared/utils/index.dart';

/// 图片裁剪控制器
/// 负责处理图片的正方形裁剪功能
class ImageCropController extends GetxController {
  // 裁剪相关的控制器
  final GlobalKey<ExtendedImageEditorState> editorKey =
      GlobalKey<ExtendedImageEditorState>();

  // 状态变量
  bool _isLoading = false;
  File? _originalImageFile;
  File? _croppedImageFile;

  // Getters
  bool get isLoading => _isLoading;
  File? get originalImageFile => _originalImageFile;
  File? get croppedImageFile => _croppedImageFile;

  @override
  void onInit() {
    super.onInit();
    _initializeImage();
  }

  /// 初始化图片
  void _initializeImage() {
    final arguments = Get.arguments;
    if (arguments != null && arguments is Map<String, dynamic>) {
      final imagePath = arguments['imagePath'] as String?;
      if (imagePath != null) {
        _originalImageFile = File(imagePath);
        update();
      }
    }
  }

  /// 重置裁剪区域为正方形
  void resetCropToSquare() {
    final state = editorKey.currentState;
    if (state != null) {
      // 重置到正方形裁剪
      state.reset();
      update();
    }
  }

  /// 旋转图片
  void rotateImage() {
    final state = editorKey.currentState;
    if (state != null) {
      state.rotate();
      update();
    }
  }

  /// 翻转图片
  void flipImage() {
    final state = editorKey.currentState;
    if (state != null) {
      state.flip();
      update();
    }
  }

  /// 确认裁剪
  Future<void> confirmCrop() async {
    if (_originalImageFile == null) {
      ShowToast.fail('没有可裁剪的图片');
      return;
    }

    _isLoading = true;
    update();

    try {
      final state = editorKey.currentState;
      if (state == null) {
        throw Exception('编辑器状态异常');
      }

      // 获取裁剪区域和原始图片数据
      final cropRect = state.getCropRect();
      final rawImageData = state.rawImageData;

      if (cropRect == null) {
        throw Exception('无法获取裁剪区域');
      }

      // 执行实际的图片裁剪
      final croppedImageData = await _cropImageData(rawImageData, cropRect);
      if (croppedImageData == null) {
        throw Exception('图片裁剪失败');
      }

      // 保存裁剪后的图片
      final croppedFile = await _saveCroppedImage(croppedImageData);
      _croppedImageFile = croppedFile;

      ShowToast.success('裁剪完成');

      // 返回裁剪后的图片路径
      Get.back(result: {
        'croppedImagePath': croppedFile.path,
        'originalImagePath': _originalImageFile!.path,
      });
    } catch (e) {
      ShowToast.fail('裁剪失败: ${e.toString()}');
    } finally {
      _isLoading = false;
      update();
    }
  }

  /// 裁剪图片数据
  Future<Uint8List?> _cropImageData(
      Uint8List rawImageData, Rect cropRect) async {
    try {
      // 解码原始图片数据
      final originalImage = img.decodeImage(rawImageData);
      if (originalImage == null) {
        Console.log('无法解码原始图片数据');
        return null;
      }

      Console.log('原始图片尺寸: ${originalImage.width}x${originalImage.height}');
      Console.log('裁剪区域: ${cropRect.toString()}');

      // 确保裁剪区域在图片范围内
      final left =
          cropRect.left.clamp(0, originalImage.width.toDouble()).toInt();
      final top =
          cropRect.top.clamp(0, originalImage.height.toDouble()).toInt();
      final width =
          (cropRect.width.clamp(0, originalImage.width - left.toDouble()))
              .toInt();
      final height =
          (cropRect.height.clamp(0, originalImage.height - top.toDouble()))
              .toInt();

      Console.log(
          '调整后的裁剪区域: left=$left, top=$top, width=$width, height=$height');

      // 执行裁剪
      final croppedImage = img.copyCrop(
        originalImage,
        x: left,
        y: top,
        width: width,
        height: height,
      );

      Console.log('裁剪后图片尺寸: ${croppedImage.width}x${croppedImage.height}');

      // 编码为JPEG格式
      final croppedImageData = img.encodeJpg(croppedImage, quality: 90);
      return Uint8List.fromList(croppedImageData);
    } catch (e) {
      Console.log('裁剪图片数据失败: $e');
      return null;
    }
  }

  /// 保存裁剪后的图片
  Future<File> _saveCroppedImage(Uint8List imageData) async {
    try {
      // 获取临时目录
      final tempDir = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'cropped_image_$timestamp.jpg';
      final filePath = '${tempDir.path}/$fileName';

      // 写入文件
      final file = File(filePath);
      await file.writeAsBytes(imageData);

      Console.log('裁剪后的图片已保存: $filePath');
      return file;
    } catch (e) {
      throw Exception('保存裁剪图片失败: ${e.toString()}');
    }
  }

  /// 取消裁剪
  void cancelCrop() {
    Get.back();
  }
}
