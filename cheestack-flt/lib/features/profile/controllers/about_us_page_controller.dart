import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:cheestack_flt/shared/utils/index.dart';

class AboutUsController extends GetxController {
  String version = "";
  late WebViewController agreementWebController;
  late WebViewController privacyWebController;

  // 控制加载状态
  final ValueNotifier<bool> isAgreementWebLoading = ValueNotifier(true);
  final ValueNotifier<bool> isPrivacyWebLoading = ValueNotifier(true);

  AboutUsController();

  Future init() async {
    update();
  }

  void onTap() {}

  @override
  void onInit() {
    super.onInit();
    _initData();
    _initWebViews();
  }

  Future<void> _initData() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    version = packageInfo.version;
    update();
  }

  /// 初始化所有WebView控制器
  Future<void> _initWebViews() async {
    privacyWebController = await _createWebViewController(
      'http://cs.chivetech.com/api/v1/general/privacy',
      isPrivacyWebLoading,
    );

    agreementWebController = await _createWebViewController(
      'http://cs.chivetech.com/api/v1/general/agreement',
      isAgreementWebLoading,
    );
  }

  /// 创建WebView控制器的工厂方法
  Future<WebViewController> _createWebViewController(
    String url,
    ValueNotifier<bool> loadingNotifier,
  ) async {
    final controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.white)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            if (progress == 100) {
              loadingNotifier.value = false;
            }
          },
          onPageStarted: (String url) {
            loadingNotifier.value = true;
          },
          onPageFinished: (String url) async {
            loadingNotifier.value = false;
            update();
          },
          onHttpError: (HttpResponseError error) {
            loadingNotifier.value = false;
          },
          onWebResourceError: (WebResourceError error) {
            ShowToast.fail('加载出错: ${error.description}');
            loadingNotifier.value = false;
          },
          onNavigationRequest: (NavigationRequest request) {
            if (!request.url.startsWith('http://cs.chivetech.com')) {
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
        ),
      );
    await controller.loadRequest(Uri.parse(url));
    update();
    return controller;
  }

  @override
  void onReady() async {
    super.onReady();
    await init();
  }

  showAgreementDialog() async {
    Skeyboard.hide();
    Sdialog.show(
      // title: const Text("用户协议"),
      context: Get.context!,
      onConfirm: () {
        Get.back();
        update();
      },
      confirm: const Text('同意'),
      builder: (BuildContext context) {
        return SizedBox(
          width: Get.width * 0.9,
          height: Get.height * 0.7,
          child: Stack(
            children: [
              WebViewWidget(controller: agreementWebController),
              ValueListenableBuilder<bool>(
                valueListenable: isAgreementWebLoading,
                builder: (context, isLoading, child) {
                  return isLoading
                      ? const Center(
                          child: CircularProgressIndicator(),
                        )
                      : const SizedBox.shrink();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  showPrivacyDialog() async {
    Skeyboard.hide();
    Sdialog.show(
      context: Get.context!,
      onConfirm: () {
        Get.back();
      },
      confirm: const Text('同意'),
      builder: (BuildContext context) {
        return SizedBox(
          width: Get.width * 0.9,
          height: Get.height * 0.7,
          child: Stack(
            children: [
              WebViewWidget(controller: privacyWebController),
              ValueListenableBuilder<bool>(
                valueListenable: isPrivacyWebLoading,
                builder: (context, isLoading, child) {
                  return isLoading
                      ? const Center(
                          child: CircularProgressIndicator(),
                        )
                      : const SizedBox.shrink();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  // @override
  // void onClose() {
  //   super.onClose();
  // }
}
