import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cheestack_flt/widgets/index.dart';
import 'package:cheestack_flt/theme.dart';

/// 通用的弹窗选择项组件
/// 用于所有设置弹窗中的选择项，确保统一的视觉风格
class UnifiedOptionItem extends StatelessWidget {
  /// 主标题
  final String title;
  
  /// 点击回调
  final VoidCallback? onTap;
  
  /// 左侧图标
  final IconData? icon;
  
  /// 副标题/描述
  final String? subtitle;
  
  /// 自定义右侧内容
  final Widget? trailing;
  
  /// 图标颜色
  final Color? iconColor;
  
  /// 是否选中状态
  final bool isSelected;
  
  /// 是否为当前用户（账户切换时使用）
  final bool isCurrentUser;
  
  /// 是否显示右侧箭头
  final bool showArrow;

  const UnifiedOptionItem({
    Key? key,
    required this.title,
    required this.onTap,
    this.icon,
    this.subtitle,
    this.trailing,
    this.iconColor,
    this.isSelected = false,
    this.isCurrentUser = false,
    this.showArrow = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bool isHighlighted = isSelected || isCurrentUser;
    
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(16.w),
        margin: EdgeInsets.only(bottom: 8.h),
        decoration: BoxDecoration(
          color: isHighlighted
              ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: isHighlighted
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
            width: isHighlighted ? 2.w : 1.w,
          ),
        ),
        child: Row(
          children: [
            // 左侧图标（可选）
            if (icon != null) ...[
              Container(
                width: 40.w,
                height: 40.w,
                decoration: BoxDecoration(
                  color: isHighlighted
                      ? Theme.of(context).colorScheme.primary
                      : (iconColor ?? Theme.of(context).colorScheme.primary)
                          .withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20.r),
                ),
                child: Icon(
                  icon,
                  size: 20.w,
                  color: isHighlighted
                      ? Theme.of(context).colorScheme.onPrimary
                      : (iconColor ?? Theme.of(context).colorScheme.primary),
                ),
              ),
              SizedBox(width: 16.w),
            ],

            // 文字内容
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          title,
                          fontSize: 16.sp,
                          
                          color: isHighlighted
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      // 当前用户标识
                      if (isCurrentUser) ...[
                        SizedBox(width: 8.w),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 8.w,
                            vertical: 2.h,
                          ),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.primary,
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                          child: Text(
                            '当前',
                            style: TextStyle(
                                    fontSize: 10.sp, color: Theme.of(context))
                                .colorScheme
                                .onPrimary,
                          ),
                        ),
                      ],
                    ],
                  ),
                  if (subtitle != null) ...[
                    SizedBox(height: 4.h),
                    Text(
                      subtitle!,
                      style:
                          TextStyle(fontSize: 12.sp, color: Theme.of(context))
                              .colorScheme
                              .onSurfaceVariant,
                    ),
                  ],
                ],
              ),
            ),

            // 右侧内容
            if (trailing != null) ...[
              SizedBox(width: 12.w),
              trailing!,
            ] else if (showArrow && onTap != null) ...[
              SizedBox(width: 12.w),
              Icon(
                Icons.arrow_forward_ios,
                size: 16.w,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ] else if (isSelected) ...[
              Icon(
                Icons.check_circle,
                color: Theme.of(context).colorScheme.primary,
                size: 20.w,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
