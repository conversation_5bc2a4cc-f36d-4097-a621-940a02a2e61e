import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/shared/utils/index.dart';
import 'package:cheestack_flt/controllers/index.dart';
import '../models/profile_model.dart';
import '../controllers/profile_controller.dart';
import 'profile_api.dart';

/// Profile 服务层
/// 负责处理用户配置文件相关的网络请求和数据持久化
class ProfileService {
  /// 获取ProfileAPI服务实例
  ProfileApiService get _profileApi => Get.find<ProfileApiService>();

  /// 更新用户配置
  ///
  /// [config] 用户配置数据
  /// 返回更新后的配置数据
  Future<ConfigModel> updateUserConfig(Map<String, dynamic> config) async {
    final response = await _profileApi.updateUserConfig(configData: config);
    if (response.isSuccess) {
      return response.data!;
    }
    throw Exception('更新用户配置失败: ${response.message ?? "未知错误"}');
  }

  /// 更新用户资料
  ///
  /// [profile] 用户资料数据
  /// 返回更新后的用户数据
  Future<UserModel> updateUserProfile(Map<String, dynamic> profile) async {
    final response = await _profileApi.updateUserProfile(profileData: profile);
    if (response.isSuccess) {
      return response.data!;
    }
    throw Exception('更新用户资料失败: ${response.message ?? "未知错误"}');
  }

  /// 获取用户配置
  ///
  /// 返回用户配置数据
  Future<ConfigModel> getUserConfig() async {
    final response = await _profileApi.getUserConfig();
    if (response.isSuccess) {
      return response.data!;
    }
    throw Exception('获取用户配置失败: ${response.message ?? "未知错误"}');
  }

  /// 获取用户资料
  ///
  /// 返回用户资料数据
  Future<UserModel> getUserProfile() async {
    final response = await _profileApi.getUserProfile();
    if (response.isSuccess) {
      return response.data!;
    }
    throw Exception('获取用户资料失败: ${response.message ?? "未知错误"}');
  }

  /// 上传用户头像
  ///
  /// [imagePath] 图片文件路径
  /// 返回上传后的头像URL
  Future<String> uploadAvatar(String imagePath) async {
    final response = await _profileApi.uploadAvatar(avatarFile: imagePath);
    if (response.isSuccess) {
      return response.data!;
    }
    throw Exception('上传头像失败: ${response.message ?? "未知错误"}');
  }

  /// 修改密码
  ///
  /// [request] 修改密码请求数据
  /// 返回修改结果
  Future<bool> changePassword(ChangePasswordRequest request) async {
    try {
      final response = await _profileApi.updatePassword(
        oldPassword: request.currentPassword,
        newPassword: request.newPassword,
      );
      return response.isSuccess;
    } catch (e) {
      throw Exception('修改密码失败: $e');
    }
  }

  /// 删除用户账户
  ///
  /// 返回删除结果
  Future<bool> deleteAccount() async {
    try {
      final response = await _profileApi.deleteAccount(password: '');
      return response.isSuccess;
    } catch (e) {
      return false;
    }
  }

  /// 获取当前主题模式
  ///
  /// 返回当前设置的主题模式
  AppThemeMode getCurrentThemeMode() {
    final themeValue = StorageService.to.getString(C.localThemeMode);
    return AppThemeModeExtension.fromStorageValue(themeValue);
  }

  /// 设置主题模式
  ///
  /// [themeMode] 要设置的主题模式
  /// 返回设置是否成功
  Future<bool> setThemeMode(AppThemeMode themeMode) async {
    try {
      // 保存到本地存储
      await StorageService.to
          .setString(C.localThemeMode, themeMode.storageValue);

      // 更新应用主题
      switch (themeMode) {
        case AppThemeMode.light:
          AppTheme.mode = ThemeMode.light;
          break;
        case AppThemeMode.dark:
          AppTheme.mode = ThemeMode.dark;
          break;
        case AppThemeMode.system:
          AppTheme.mode = ThemeMode.system;
          break;
      }

      // 设置系统UI样式
      AppTheme.setSystemStyle();

      // 通知GetX更新主题
      Get.changeThemeMode(AppTheme.mode);

      return true;
    } catch (e) {
      return false;
    }
  }

  /// 获取当前语言设置
  ///
  /// 返回当前设置的语言
  AppLanguage getCurrentLanguage() {
    final configStore = Get.find<ConfigStore>();
    return AppLanguageExtension.fromLocale(configStore.locale);
  }

  /// 设置语言
  ///
  /// [language] 要设置的语言
  /// 返回设置是否成功R
  Future<bool> setLanguage(AppLanguage language) async {
    try {
      final configStore = Get.find<ConfigStore>();
      configStore.setLanguage(language.locale);
      return true;
    } catch (e) {
      return false;
    }
  }
}
