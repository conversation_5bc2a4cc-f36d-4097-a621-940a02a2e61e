import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cheestack_flt/routes/index.dart';
import 'package:cheestack_flt/global.dart';
import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/shared/utils/index.dart';
import 'package:cheestack_flt/shared/utils/oxhttp/index.dart';

/// 本地数据库版本的认证控制器
/// 使用本地数据库服务替代直接API调用
class LocalAuthController extends GetxController {
  static LocalAuthController get to => Get.find();

  // 服务实例
  late final UserDataService _userDataService;
  late final ApiSyncService _apiSyncService;

  // ==================== 认证相关状态 ====================

  // 登录状态
  final RxBool _isLoggingIn = false.obs;
  bool get isLoggingIn => _isLoggingIn.value;

  // 协议同意状态
  final RxBool _isAgreed = false.obs;
  bool get isAgreed => _isAgreed.value;

  // 国家代码
  final RxString _countryCode = '+86'.obs;
  String get countryCode => _countryCode.value;

  // 验证码倒计时
  final RxInt _countdown = 0.obs;
  int get countdown => _countdown.value;
  Timer? _countdownTimer;

  // 用户数据
  Rx<UserModel?> get currentUser => _userDataService.currentUser.obs;
  Rx<ConfigModel?> get currentConfig => _userDataService.currentConfig.obs;

  // 表单控制器
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController verificationCodeController =
      TextEditingController();

  // 表单验证Key
  final GlobalKey<FormState> phoneFormKey = GlobalKey<FormState>();
  final GlobalKey<FormState> verificationFormKey = GlobalKey<FormState>();

  @override
  void onInit() {
    super.onInit();
    _initServices();
    _loadUserData();
  }

  @override
  void onClose() {
    _countdownTimer?.cancel();
    phoneController.dispose();
    verificationCodeController.dispose();
    super.onClose();
  }

  /// 初始化服务
  void _initServices() {
    _userDataService = UserDataService.to;
    _apiSyncService = ApiSyncService.to;
  }

  /// 加载用户数据
  void _loadUserData() {
    // 用户数据已经在UserDataService中管理
    update();
  }



  /// 切换协议同意状态
  void toggleAgreement() {
    _isAgreed.value = !_isAgreed.value;
  }

  /// 设置国家代码
  void setCountryCode(String code) {
    _countryCode.value = code;
  }

  /// 检查协议同意状态
  bool _checkAgreement() {
    if (!_isAgreed.value) {
      ShowToast.fail('请先同意用户协议和隐私政策');
      return false;
    }
    return true;
  }



  /// 跳转到手机号登录页面
  void goToPhoneLogin() {
    // 本地控制器版本，无需页面切换
    // 可以在这里重置表单状态
    phoneController.clear();
    verificationCodeController.clear();
  }

  /// 发送验证码
  Future<void> sendVerificationCode() async {
    if (!phoneFormKey.currentState!.validate()) return;
    if (!_checkAgreement()) return;

    try {
      final mobile = phoneController.text.trim();

      // 调用API发送验证码
      final response = await OxHttp.to.post('/api/auth/get_captcha', data: {
        'mobile': mobile,
      });

      if (response.statusCode == 200) {
        ShowToast.success('验证码已发送');
        _startCountdown();
        // 使用路由跳转到验证码页面
        Get.toNamed(AppRoutes.verificationCode);
      } else {
        ShowToast.fail(response.data['message'] ?? '发送验证码失败');
      }
    } catch (e) {
      Console.log('Send verification code error: $e');
      ShowToast.fail('发送验证码失败，请检查网络连接');
    }
  }

  /// 验证验证码并登录
  Future<void> verifyCodeAndLogin() async {
    if (!verificationFormKey.currentState!.validate()) return;

    _isLoggingIn.value = true;
    update();

    try {
      final mobile = phoneController.text.trim();
      final code = verificationCodeController.text.trim();

      final success = await _performLogin(mobile, code);

      if (success) {
        await _handleLoginSuccess();
      } else {
        ShowToast.fail('登录失败，请检查验证码');
      }
    } catch (e) {
      Console.log('Verify code and login error: $e');
      ShowToast.fail('登录失败，请重试');
    } finally {
      _isLoggingIn.value = false;
      update();
    }
  }

  /// 执行登录
  Future<bool> _performLogin(String mobile, String verificationCode) async {
    try {
      // 使用UserDataService进行登录
      return await _userDataService.loginUser(mobile, verificationCode);
    } catch (e) {
      Console.log('Perform login error: $e');
      return false;
    }
  }

  /// 处理登录成功
  Future<void> _handleLoginSuccess() async {
    try {
      // 初始化Gromore（如果需要）
      await Global.initGromore();

      // 触发全量数据同步
      _apiSyncService.syncAllData();

      // 跳转到主页
      Get.offAllNamed(AppRoutes.application);

      ShowToast.success('登录成功');
    } catch (e) {
      Console.log('Handle login success error: $e');
    }
  }

  /// 开始倒计时
  void _startCountdown() {
    _countdown.value = 60;
    _countdownTimer?.cancel();
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countdown.value > 0) {
        _countdown.value--;
      } else {
        timer.cancel();
      }
    });
  }

  /// 登出
  Future<void> logout() async {
    try {
      await _userDataService.logoutUser();

      // 跳转到登录页
      Get.offAllNamed(AppRoutes.login);

      ShowToast.success('已退出登录');
    } catch (e) {
      Console.log('Logout error: $e');
      ShowToast.fail('退出登录失败');
    }
  }

  /// 检查是否已登录
  bool isLoggedIn() {
    return _userDataService.isLoggedIn();
  }

  /// 获取当前用户
  UserModel? getCurrentUser() {
    return _userDataService.currentUser;
  }

  /// 获取用户配置
  ConfigModel? getUserConfig() {
    return _userDataService.currentConfig;
  }
}
