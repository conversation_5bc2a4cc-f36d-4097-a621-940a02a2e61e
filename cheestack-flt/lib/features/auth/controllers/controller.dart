import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:cheestack_flt/routes/index.dart';
import 'package:cheestack_flt/global.dart';
import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/apis/index.dart';
import 'package:cheestack_flt/services/dao/dao_manager.dart';
import 'package:cheestack_flt/shared/extensions/index.dart';
import 'package:cheestack_flt/shared/utils/oxhttp/index.dart';
import 'package:cheestack_flt/shared/utils/index.dart';
import 'package:cheestack_flt/shared/utils/error_handler.dart';
import '../models/auth_model.dart' as auth;
import '../apis/auth_api.dart';


/// 认证和用户管理控制器
/// 负责管理认证相关的状态和业务逻辑，以及用户数据管理
class AuthController extends GetxController {
  // 单例模式
  static AuthController get to => Get.find();

  // 服务实例
  late final AuthApiService _authApiService;

  // ==================== 认证相关状态 ====================

  // ==================== 用户管理相关状态 ====================
  // 多用户管理
  List<String> usernames = [];
  UsrModel usr = UsrModel();

  // 登录状态
  bool isLogin = false;

  // 是否已接受隐私协议
  bool isAcceptPrivacy = false;

  // 表单控制器
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController verificationCodeController =
      TextEditingController();

  // 4个独立的验证码输入控制器
  final List<TextEditingController> codeControllers = List.generate(
    4,
    (index) => TextEditingController(),
  );

  // 4个独立的焦点节点
  final List<FocusNode> codeFocusNodes = List.generate(
    4,
    (index) => FocusNode(),
  );

  // 表单验证Key
  final GlobalKey<FormState> phoneFormKey = GlobalKey<FormState>();
  final GlobalKey<FormState> verificationFormKey = GlobalKey<FormState>();

  // 验证码相关状态
  final RxBool _isSendingCode = false.obs;
  final RxInt _countdown = 0.obs;
  Timer? _countdownTimer;

  bool get isSendingCode => _isSendingCode.value;
  int get countdown => _countdown.value;
  bool get canSendCode => _countdown.value == 0 && !_isSendingCode.value;

  // 协议同意状态
  final RxBool _isAgreed = false.obs;
  bool get isAgreed => _isAgreed.value;

  // 登录状态
  final RxBool _isLoggingIn = false.obs;
  bool get isLoggingIn => _isLoggingIn.value;

  // 国家代码
  final RxString _countryCode = '+86'.obs;
  String get countryCode => _countryCode.value;

  // 是否显示验证码输入框
  final RxBool _showVerificationCodeInput = false.obs;
  bool get showVerificationCodeInput => _showVerificationCodeInput.value;



  /// 清空验证码输入
  void clearVerificationCode() {
    // 清空主要的验证码控制器
    verificationCodeController.clear();
    // 如果还有旧的单独控制器，也清空它们（向后兼容）
    for (var controller in codeControllers) {
      controller.clear();
    }
  }

  @override
  void onInit() {
    super.onInit();
    _authApiService = Get.find<AuthApiService>();
    _initController();
    // 异步初始化用户数据
    _initUserData();
  }

  @override
  void onClose() {
    phoneController.dispose();
    verificationCodeController.dispose();

    // 清理验证码输入控制器和焦点节点
    for (var controller in codeControllers) {
      controller.dispose();
    }
    for (var focusNode in codeFocusNodes) {
      focusNode.dispose();
    }

    _countdownTimer?.cancel();
    super.onClose();
  }

  /// 初始化控制器
  void _initController() {
    // 检查协议同意状态
    try {
      _isAgreed.value = isAcceptPrivacy;
    } catch (e) {
      // 测试环境时的默认值
      _isAgreed.value = false;
    }
  }

  /// 初始化用户数据（合并自UserStore）
  Future<void> _initUserData() async {
    // 获取用户数据
    await getUserDataFromCache();
    // 获取是否已登录
    isLogin = StorageService.to.getBool(C.isLogin);
    update();
  }



  /// 切换协议同意状态
  void toggleAgreement() {
    _isAgreed.value = !_isAgreed.value;
  }

  /// 设置国家代码
  void setCountryCode(String code) {
    _countryCode.value = code;
  }





  /// 跳转到手机号登录页面
  void goToPhoneLogin() {
    if (!_checkAgreement()) return;
    // AuthPage本身就是手机号登录页面，无需跳转
    // 如果需要，可以重置表单状态
    phoneController.clear();
    verificationCodeController.clear();
  }

  /// 跳转到密码登录页面
  void goToPasswordLogin() {
    Get.toNamed(AppRoutes.passwordLogin);
  }

  /// 检查协议并执行操作
  Future<void> checkAgreementAndExecute(VoidCallback callback) async {
    if (_isAgreed.value) {
      callback();
    } else {
      final result = await showAgreementDialog();
      if (result == true) {
        _isAgreed.value = true;
        update();
        callback();
      }
    }
  }

  /// 显示隐私协议确认底部弹窗
  Future<bool?> showAgreementDialog() async {
    return await Get.bottomSheet<bool>(
      Container(
        decoration: BoxDecoration(
          color: Get.theme.colorScheme.surface,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r),
            topRight: Radius.circular(20.r),
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: EdgeInsets.all(24.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 顶部指示器
                Container(
                  width: 40.w,
                  height: 4.h,
                  decoration: BoxDecoration(
                    color: Get.theme.colorScheme.onSurfaceVariant
                        .withValues(alpha: 0.4),
                    borderRadius: BorderRadius.circular(2.r),
                  ),
                ),
                SizedBox(height: 24.h),

                // 标题
                Row(
                  children: [
                    Icon(
                      Icons.privacy_tip_outlined,
                      size: 28.w,
                      color: Get.theme.colorScheme.primary,
                    ),
                    SizedBox(width: 12.w),
                    Text(
                      '隐私协议确认',
                    ),
                  ],
                ),
                SizedBox(height: 8.h),

                // 描述文字
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    '为了保护您的隐私和数据安全，请您仔细阅读并同意《用户协议》和《隐私政策》后继续使用我们的服务。',
                    
                    
                  ),
                ),
                SizedBox(height: 32.h),

                // 同意并继续按钮
                SizedBox(
                  width: double.infinity,
                  child: Sbutton(
                    backgroundColor: Get.theme.colorScheme.primary,
                    foregroundColor: Get.theme.colorScheme.onPrimary,
                    onPressed: () => Get.back(result: true),
                    child: Text(
                      '同意并继续',
                    ),
                  ),
                ),
                SizedBox(height: 12.h),

                // 取消按钮
                SizedBox(
                  width: double.infinity,
                  child: Sbutton(
                    backgroundColor:
                        Get.theme.colorScheme.surfaceContainerHighest,
                    foregroundColor: Get.theme.colorScheme.onSurfaceVariant,
                    onPressed: () => Get.back(result: false),
                    child: Text(
                      '取消',
                      
                    ),
                  ),
                ),
                SizedBox(height: 16.h),
              ],
            ),
          ),
        ),
      ),
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      isDismissible: false,
    );
  }

  /// 发送验证码
  Future<void> sendVerificationCode() async {
    if (!phoneFormKey.currentState!.validate()) return;

    await checkAgreementAndExecute(() async {
      _isSendingCode.value = true;
      update();

      try {
        print('🚀 开始发送验证码到: ${phoneController.text}');
        final response =
            await _authApiService.getCaptcha(phone: phoneController.text);

        print(
            '📱 验证码发送响应: success=${response.isSuccess}, message=${response.message}');

        if (response.isSuccess) {
          print('✅ 验证码发送成功，跳转到验证码页面');
          _startCountdown();
          // 清空之前的验证码输入
          clearVerificationCode();
          // 跳转到验证码输入页面
          goToVerificationCodePage();
        } else {
          print('❌ 验证码发送失败: ${response.message}');
          ShowToast.fail(response.message ?? '验证码发送失败');
        }
      } catch (e) {
        print('💥 验证码发送异常: $e');
        ShowToast.fail('验证码发送失败：${e.toString()}');
      } finally {
        _isSendingCode.value = false;
        update();
      }
    });
  }

  /// 跳转到验证码输入页面
  void goToVerificationCodePage() {
    Get.toNamed('/verification-code');
  }

  /// 重新发送验证码
  Future<void> resendVerificationCode() async {
    if (!canSendCode) return;

    _isSendingCode.value = true;
    update();

    try {
      print('🔄 重新发送验证码到: ${phoneController.text}');
      final response =
          await _authApiService.getCaptcha(phone: phoneController.text);

      print(
          '📱 重新发送响应: success=${response.isSuccess}, message=${response.message}');

      if (response.isSuccess) {
        print('✅ 验证码重新发送成功');
        _startCountdown();
        // 清空之前的验证码输入
        clearVerificationCode();
        // 自动聚焦到验证码输入框
        // 注意：现在使用Pinput组件，会自动聚焦
      } else {
        print('❌ 验证码重新发送失败: ${response.message}');
        ShowToast.fail(response.message ?? '验证码发送失败');
      }
    } catch (e) {
      print('💥 验证码重新发送异常: $e');
      ShowToast.fail('验证码发送失败：${e.toString()}');
    } finally {
      _isSendingCode.value = false;
      update();
    }
  }

  /// 验证验证码并登录
  Future<void> verifyCodeAndLogin() async {
    // 检查验证码是否完整 - 直接从verificationCodeController获取
    final code = verificationCodeController.text.trim();
    print('🔐 开始验证码登录验证');
    print('手机号: ${phoneController.text}');
    print('验证码: $code');
    print('验证码长度: ${code.length}');

    if (code.length != 4) {
      print('❌ 验证码长度不正确: ${code.length}');
      ShowToast.fail('请输入完整的4位验证码');
      return;
    }

    _isLoggingIn.value = true;
    update();

    try {
      print('📡 发送快速登录请求...');
      final response = await _authApiService.quickLogin(
        phone: phoneController.text,
        code: code,
      );

      print('📱 快速登录响应:');
      print('  - 成功状态: ${response.isSuccess}');
      print('  - 错误状态: ${response.isFailure}');
      print('  - 状态码: ${response.statusCode}');
      print('  - 错误码: ${response.errorCode}');
      print('  - 消息: ${response.message}');
      print('  - 数据为空: ${response.data == null}');

      if (response.data != null) {
        print('  - 用户ID: ${response.data!.user.id}');
        print('  - 用户名: ${response.data!.user.username}');
        print('  - 手机号: ${response.data!.user.mobile}');
        print('  - 是否新用户: ${response.data!.isNewUser}');
        print('  - Token ID: ${response.data!.auth.id}');
      }

      if (response.isSuccess && response.data != null) {
        print('✅ 登录成功，处理登录成功逻辑');
        ShowToast.success('登录成功');
        await _handleLoginSuccess(response.data!);
      } else {
        print('❌ 登录失败');
        print('失败原因: ${response.message ?? '未知错误'}');
        final friendlyMessage =
            ErrorHandler.getUserFriendlyMessage(response.message ?? '验证码错误');
        ShowToast.fail(friendlyMessage);
        // 清空验证码输入
        clearVerificationCode();
      }
    } catch (e, stackTrace) {
      print('💥 登录异常: $e');
      print('堆栈跟踪: $stackTrace');
      final friendlyMessage = ErrorHandler.getUserFriendlyMessage(e);
      ShowToast.fail(friendlyMessage);
      // 清空验证码输入
      clearVerificationCode();
    } finally {
      _isLoggingIn.value = false;
      update();
      print('🔚 登录流程结束');
    }
  }



  /// 返回上一页
  void goBack() {
    // 使用Get路由的返回功能
    Get.back();
  }

  /// 开始倒计时
  void _startCountdown() {
    _countdown.value = 60;
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countdown.value > 0) {
        _countdown.value--;
      } else {
        timer.cancel();
      }
    });
  }

  /// 检查协议同意状态
  bool _checkAgreement() {
    if (!isAgreed) {
      _showAgreementDialog();
      return false;
    }
    return true;
  }

  /// 显示协议同意对话框
  void _showAgreementDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('提示'),
        content: const Text('请先同意用户协议和隐私政策'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              toggleAgreement();
            },
            child: const Text('同意'),
          ),
        ],
      ),
    );
  }

  /// 处理登录成功（公开方法）
  Future<void> handleLoginSuccess(auth.AuthResponse authResponse) async {
    await _handleLoginSuccess(authResponse);
  }

  /// 处理登录成功（私有方法）
  Future<void> _handleLoginSuccess([auth.AuthResponse? authResponse]) async {
    try {
      print('🔄 开始处理登录成功逻辑');

      if (authResponse != null) {
        print('📱 收到登录响应数据:');
        print('  - 用户ID: ${authResponse.user.id}');
        print('  - 用户名: ${authResponse.user.username}');
        print('  - 手机号: ${authResponse.user.mobile}');
        print('  - 是否新用户: ${authResponse.isNewUser}');

        // 如果是新用户，清除所有旧的用户数据
        if (authResponse.isNewUser) {
          print('🆕 检测到新用户，清除所有旧的用户数据');
          // 清除用户名列表
          usernames.clear();
          // 清除登录状态
          isLogin = false;
          // 更新本地存储
          await StorageService.to.setStringList(C.usernames, []);
          await StorageService.to.setBool(C.isLogin, false);
        }

        // 初始化Gromore（如果需要）
        await Global.initGromore();

        // 创建完整的UsrModel，使用本地的多用户存储机制
        final userData = _createUsrModelFromAuthResponse(authResponse);

        await loginSuccess(userData);
      }

      // 更新协议同意状态
      await updateIsAcceptPrivacy(privacy: true);

      // 跳转到主页
      print('🔄 跳转到启动页面');
      Get.offAllNamed(AppRoutes.splash);

      print('✅ 登录成功处理完成');
    } catch (e) {
      print('❌ 登录处理失败: $e');
      final friendlyMessage = ErrorHandler.getUserFriendlyMessage(e);
      ShowToast.fail(friendlyMessage);
      debugPrint(e.toString());
    }
  }

  /// 验证手机号格式
  String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入手机号';
    }
    final RegExp phoneRegExp = RegExp(r'^1[3-9]\d{9}$');
    if (!phoneRegExp.hasMatch(value)) {
      return '请输入正确的手机号';
    }
    return null;
  }

  /// 验证验证码格式
  String? validateVerificationCode(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入验证码';
    }
    if (value.length != 4) {
      return '请输入4位验证码';
    }
    return null;
  }

  /// 从AuthResponse创建UsrModel
  UsrModel _createUsrModelFromAuthResponse(auth.AuthResponse authResponse) {
    print('🔄 开始创建UsrModel');
    print('服务器返回的用户信息:');
    print('  - 用户ID: ${authResponse.user.id}');
    print('  - 用户名: ${authResponse.user.username}');
    print('  - 手机号: ${authResponse.user.mobile}');
    print('  - 是否新用户: ${authResponse.isNewUser}');

    final usrModel = UsrModel(
      user: _createUserModelFromAuthModel(authResponse.user),
      auth: _createAuthModelFromTokens(authResponse.auth),
      config: _createConfigModelFromUserConfig(authResponse.config),
    );

    print('✅ UsrModel创建完成');
    return usrModel;
  }

  /// 从AuthModel创建UserModel
  UserModel _createUserModelFromAuthModel(auth.AuthModel authModel) {
    return UserModel(
      id: authModel.id,
      username: authModel.username,
      mobile: authModel.mobile,
      email: authModel.email,
      avatar: authModel.avatar,
      intro: authModel.intro,
      createdAt: authModel.createdAt != null
          ? DateTime.tryParse(authModel.createdAt!)
          : null,
      updatedAt: authModel.updatedAt != null
          ? DateTime.tryParse(authModel.updatedAt!)
          : null,
    );
  }

  /// 从AuthTokens创建AuthModel
  AuthModel _createAuthModelFromTokens(auth.AuthTokens tokens) {
    return AuthModel(
      id: tokens.id,
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
    );
  }

  /// 从UserConfig创建ConfigModel
  ConfigModel _createConfigModelFromUserConfig(auth.UserConfig userConfig) {
    return ConfigModel(
      id: null, // UserConfig没有id字段
      isAutoPlayAudio: userConfig.isAutoPlayAudio,
      isAutoPlayAiAudio: userConfig.isAutoPlayAiAudio,
      reviewNumber: userConfig.reviewNumber,
      studyNumber: userConfig.studyNumber,
      studyType: userConfig.studyType,
      currentStudyId: userConfig.currentStudyId != null
          ? int.tryParse(userConfig.currentStudyId!)
          : null,
      editingBookId: userConfig.editingBookId != null
          ? int.tryParse(userConfig.editingBookId!)
          : null,
    );
  }

  // ==================== 用户管理方法（合并自UserStore） ====================

  /// 登录成功处理
  Future<void> loginSuccess(UsrModel data) async {
    print('🔄 开始处理登录成功逻辑');
    print('新用户数据: ${data.user?.username} (ID: ${data.user?.id})');
    print('当前用户数据: ${usr.user?.username} (ID: ${usr.user?.id})');

    usr = data;
    isLogin = true;
    usernames.insert(0, usr.user?.username ?? "");

    print('更新后的用户列表: $usernames');
    print('当前用户: ${usr.user?.username}');

    await updateUsr();
    update();

    print('✅ 登录成功处理完成');
  }

  /// 退出登录
  Future<void> logout() async {
    Console.log("退出登录");
    try {
      usernames.remove(usr.user?.username);
      StorageService.to.remove(usr.user?.username ?? "");
      usr = UsrModel();
      isLogin = false;
      await updateUsr();
      await StorageService.to.remove(C.isLogin);
      await Get.offAllNamed(AppRoutes.splash);
    } catch (e) {
      Console.log(e);
    }
  }

  /// 切换用户
  Future switchUser(String username) async {
    /// 从本地缓存读取
    var data = StorageService.to.getJSON(username);
    Console.log(data["user"]["username"]);
    if (data != null) {
      try {
        usr = UsrModel.fromJson(data);
        usernames.remove(username);
        usernames.insert(0, username);
        await Get.offAllNamed(AppRoutes.splash);
        await updateUsr();

        /// 从网络获取更新一次Config
        await ApiConfig.get();
      } catch (e) {
        Console.log(e);
      }
    }
  }

  /// 更新用户数据
  Future updateUsr() async {
    print('💾 开始更新用户数据到本地存储');
    print('用户名列表: $usernames');
    print('当前用户: ${usr.user?.username}');
    print('登录状态: $isLogin');

    usernames = usernames.toSet().toList();
    await StorageService.to.setStringList(C.usernames, usernames);
    await StorageService.to.setJSON(usr.user?.username ?? "", usr.toJson());
    await StorageService.to.setBool(C.isLogin, isLogin);

    // 🔧 修复：同时设置current_user_id供UserDataService使用
    if (usr.user?.id != null) {
      await StorageService.to.setString('current_user_id', usr.user!.id!);
      print('✅ 已设置current_user_id: ${usr.user!.id}');

      // 🔧 修复：将用户数据保存到本地数据库
      if (Get.isRegistered<DaoManager>()) {
        try {
          final daoManager = DaoManager.to;
          final userModel = UserModel(
            id: usr.user!.id!,
            username: usr.user!.username!,
            mobile: usr.user!.mobile ?? '',
            intro: usr.user!.intro ?? '',
            avatar: usr.user!.avatar ?? '',
            createdAt: usr.user!.createdAt ?? DateTime.now(),
            updatedAt: usr.user!.updatedAt ?? DateTime.now(),
          );

          // 检查用户是否已存在
          final existingUser = await daoManager.userDao.findById(usr.user!.id!);
          if (existingUser == null) {
            await daoManager.userDao.insert(userModel);
            print('✅ 已保存用户数据到本地数据库');
          } else {
            print('✅ 用户数据已存在于本地数据库');
          }
        } catch (e) {
          print('❌ 保存用户数据到本地数据库失败: $e');
        }
      }

      // 通知UserDataService重新加载用户数据
      if (Get.isRegistered<UserDataService>()) {
        final userDataService = UserDataService.to;
        await userDataService.reloadCurrentUser();
        print('✅ 已通知UserDataService重新加载用户数据');
      }
    }

    print('✅ 用户数据已保存到本地存储');
    update();
  }

  /// 从缓存获取用户数据
  Future<void> getUserDataFromCache() async {
    print('📱 开始从缓存加载用户数据');

    isAcceptPrivacy = StorageService.to.getBool(C.isAcceptPrivacy);
    isLogin = StorageService.to.getBool(C.isLogin);

    print('隐私协议状态: $isAcceptPrivacy');
    print('登录状态: $isLogin');

    if (!isLogin) {
      print('用户未登录，跳过加载用户数据');
      return;
    }

    usernames = StorageService.to.getStringList(C.usernames);
    print('缓存中的用户名列表: $usernames');

    if (usernames.isEmpty) {
      print('用户名列表为空，跳过加载');
      return;
    }

    var json = StorageService.to.getJSON(usernames.first);
    print('加载用户数据: ${usernames.first}');

    if (json.isEmpty) {
      print('用户数据为空，跳过加载');
      return;
    }

    usr = UsrModel.fromJson(json);
    print('✅ 成功加载用户: ${usr.user?.username} (ID: ${usr.user?.id})');

    // 🔧 修复：设置current_user_id供UserDataService使用
    if (usr.user?.id != null) {
      await StorageService.to.setString('current_user_id', usr.user!.id!);
      print('✅ 已设置current_user_id: ${usr.user!.id}');

      // 🔧 修复：将用户数据保存到本地数据库
      if (Get.isRegistered<DaoManager>()) {
        try {
          final daoManager = DaoManager.to;
          final userModel = UserModel(
            id: usr.user!.id!,
            username: usr.user!.username!,
            mobile: usr.user!.mobile ?? '',
            intro: usr.user!.intro ?? '',
            avatar: usr.user!.avatar ?? '',
            createdAt: usr.user!.createdAt ?? DateTime.now(),
            updatedAt: usr.user!.updatedAt ?? DateTime.now(),
          );

          // 检查用户是否已存在
          final existingUser = await daoManager.userDao.findById(usr.user!.id!);
          if (existingUser == null) {
            await daoManager.userDao.insert(userModel);
            print('✅ 已保存用户数据到本地数据库');
          } else {
            print('✅ 用户数据已存在于本地数据库');
          }
        } catch (e) {
          print('❌ 保存用户数据到本地数据库失败: $e');
        }
      }

      // 通知UserDataService重新加载用户数据
      if (Get.isRegistered<UserDataService>()) {
        final userDataService = UserDataService.to;
        await userDataService.reloadCurrentUser();
        print('✅ 已通知UserDataService重新加载用户数据');
      }
    }

    update();
  }

  /// 更新隐私协议同意状态
  updateIsAcceptPrivacy({bool privacy = false}) async {
    isAcceptPrivacy = privacy;
    await StorageService.to.setBool(C.isAcceptPrivacy, privacy);
  }

  /// 更新配置模型
  Future<void> updateConfigModel() async {
    await asyncRequest(
      () => OxHttp.to.put(HttpUrl.config, data: usr.config?.toJson()),
      onSuccess: (data) {
        usr.config = ConfigModel.fromJson(data);
        updateUsr();
      },
      onFailure: (error) {
        ShowToast.fail(error?.msg ?? "未知错误");
      },
    );
  }
}



/// 认证页面路由绑定
class AuthBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<AuthController>(() => AuthController());
  }
}
