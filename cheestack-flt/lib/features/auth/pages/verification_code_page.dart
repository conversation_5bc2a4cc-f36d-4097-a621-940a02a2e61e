import 'package:cheestack_flt/features/auth/controllers/controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pinput/pinput.dart';

/// 验证码页面
/// 显示发送到的手机号、4位验证码输入框和重新发送按钮
class VerificationCodePage extends StatelessWidget {
  const VerificationCodePage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AuthController>(
      builder: (controller) {
        return SafeArea(
            child: Column(
          children: [
            // 顶部导航栏
            _buildAppBar(controller, context),

            // 主要内容
            Expanded(
              child: SingleChildScrollView(
                padding: REdgeInsets.symmetric(horizontal: 24),
                child: Column(
                  children: [
                    SizedBox(height: 40.h),

                    // 提示信息
                    _buildHintSection(controller, context),

                    SizedBox(height: 60.h),

                    // 验证码输入区域
                    Form(
                      key: controller.verificationFormKey,
                      child: _buildVerificationCodeInput(controller, context),
                    ),

                    SizedBox(height: 40.h),

                    // 重新发送按钮
                    _buildResendButton(controller, context),

                    SizedBox(height: 60.h),

                    // 登录按钮
                    _buildLoginButton(controller, context),

                    SizedBox(height: 40.h),
                  ],
                ),
              ),
            ),
          ],
        ));
      },
    );
  }

  /// 构建顶部导航栏
  Widget _buildAppBar(AuthController controller, BuildContext context) {
    return Container(
      height: 56.h,
      padding: REdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => controller.goBack(),
            child: Icon(
              Icons.arrow_back_ios,
              size: 20.sp,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const Spacer(),
          Text(
            '反馈',
            style: TextStyle(
              fontSize: 16.sp,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建提示信息区域
  Widget _buildHintSection(AuthController controller, BuildContext context) {
    return Column(
      children: [
        Text(
          '验证码已发送至',
          style: TextStyle(
            fontSize: 16.sp,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          '${controller.countryCode} ${controller.phoneController.text}',
          style: TextStyle(
            fontSize: 18.sp,
            
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  /// 构建验证码输入区域
  Widget _buildVerificationCodeInput(
      AuthController controller, BuildContext context) {
    // 默认样式
    final defaultPinTheme = PinTheme(
      width: 56.w,
      height: 56.h,
      textStyle: TextStyle(
        fontSize: 20.sp,
        
        color: Theme.of(context).colorScheme.onSurface,
      ),
      decoration: BoxDecoration(
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.5),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(12.r),
        color: Theme.of(context).colorScheme.surface,
      ),
    );

    // 聚焦样式
    final focusedPinTheme = defaultPinTheme.copyDecorationWith(
      border: Border.all(
        color: Theme.of(context).colorScheme.primary,
        width: 2,
      ),
      borderRadius: BorderRadius.circular(12.r),
    );

    // 已提交样式
    final submittedPinTheme = defaultPinTheme.copyWith(
      decoration: defaultPinTheme.decoration?.copyWith(
        color: Theme.of(context)
            .colorScheme
            .primaryContainer
            .withValues(alpha: 0.1),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.6),
          width: 1,
        ),
      ),
    );

    return AutofillGroup(
      child: Pinput(
        controller: controller.verificationCodeController,
        length: 4,
        defaultPinTheme: defaultPinTheme,
        focusedPinTheme: focusedPinTheme,
        submittedPinTheme: submittedPinTheme,
        pinputAutovalidateMode: PinputAutovalidateMode.onSubmit,
        showCursor: true,
        cursor: Container(
          width: 2.w,
          height: 20.h,
          color: Theme.of(context).colorScheme.primary,
        ),
        // 启用自动填充短信验证码
        autofillHints: const [AutofillHints.oneTimeCode],
        // 验证码输入完成时的回调
        onCompleted: (pin) {
          // 可以在这里添加自动验证逻辑
          print('验证码输入完成: $pin');
        },
        // 验证码改变时的回调
        onChanged: (value) {
          // 更新控制器中的验证码
          controller.verificationCodeController.text = value;
        },
        // 错误样式
        errorPinTheme: defaultPinTheme.copyDecorationWith(
          border: Border.all(
            color: Theme.of(context).colorScheme.error,
            width: 2,
          ),
        ),
        // 键盘类型
        keyboardType: TextInputType.number,
        // 输入格式器，只允许数字
        inputFormatters: [
          FilteringTextInputFormatter.digitsOnly,
        ],
        // 启用复制粘贴
        enableSuggestions: true,
        // 自动聚焦
        autofocus: true,
      ),
    );
  }

  /// 构建重新发送按钮
  Widget _buildResendButton(AuthController controller, BuildContext context) {
    return Obx(() {
      final canResend = controller.canSendCode;
      final countdown = controller.countdown;

      return TextButton(
        onPressed: canResend ? () => controller.resendVerificationCode() : null,
        child: Text(
          canResend ? '重新发送' : '重新发送 (${countdown}s)',
          style: TextStyle(
            fontSize: 14.sp,
            color: canResend
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      );
    });
  }

  /// 构建登录按钮
  Widget _buildLoginButton(AuthController controller, BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 52.h,
      child: ElevatedButton(
        onPressed: controller.isLoggingIn
            ? null
            : () => controller.verifyCodeAndLogin(),
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
          minimumSize: Size(double.infinity, 52.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
          elevation: 0,
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        ),
        child: controller.isLoggingIn
            ? SizedBox(
                width: 16.w,
                height: 16.w,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).colorScheme.onPrimary,
                  ),
                ),
              )
            : Text(
                '登录',
                style: TextStyle(
                  fontSize: 16.sp,
                  
                ),
              ),
      ),
    );
  }


}
