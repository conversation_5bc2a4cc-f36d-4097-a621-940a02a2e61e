import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../controllers/controller.dart';
import 'widgets/unified_input_field.dart';
import '../apis/auth_service.dart';
import 'package:cheestack_flt/widgets/index.dart';
import 'package:cheestack_flt/services/index.dart';

/// 账号密码登录页面
class PasswordLoginPage extends StatefulWidget {
  const PasswordLoginPage({super.key});

  @override
  State<PasswordLoginPage> createState() => _PasswordLoginPageState();
}

class _PasswordLoginPageState extends State<PasswordLoginPage> {
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  bool _isPasswordVisible = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadSavedUsername();
    // 监听用户名输入变化，用于动态显示清除按钮
    _usernameController.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  /// 加载保存的用户名
  void _loadSavedUsername() {
    final savedUsername = StorageService.to.getString('last_login_username');
    if (savedUsername.isNotEmpty) {
      _usernameController.text = savedUsername;
    }
  }

  /// 保存用户名
  Future<void> _saveUsername(String username) async {
    await StorageService.to.setString('last_login_username', username);
  }

  /// 清除保存的用户名
  Future<void> _clearSavedUsername() async {
    await StorageService.to.remove('last_login_username');
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          Get.find<AuthController>().goBack();
        }
      },
      child: Scaffold(
        backgroundColor: Theme.of(context).colorScheme.surface,
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back_ios,
              color: Theme.of(context).colorScheme.onSurface,
            ),
            onPressed: () => Get.find<AuthController>().goBack(),
          ),
          title: Text(
            '账号密码登录',
          ),
          centerTitle: true,
        ),
        body: SafeArea(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(24.w),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  SizedBox(height: 20.h),

                  // 页面标题
                  Text(
                    '欢迎回来',
                    style: TextStyle(
                      fontSize: 28.sp,
                      
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  SizedBox(height: 8.h),

                  Text(
                    '请输入您的账号和密码',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  SizedBox(height: 32.h),

                  // 用户名输入框
                  UnifiedInputField(
                    controller: _usernameController,
                    hintText: '请输入用户名或手机号',
                    prefixIcon: const Icon(Icons.person_outline),
                    suffixIcon: _usernameController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () async {
                              setState(() {
                                _usernameController.clear();
                              });
                              // 清除保存的用户名
                              await _clearSavedUsername();
                            },
                          )
                        : null,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return '请输入用户名或手机号';
                      }
                      return null;
                    },
                  ),

                  SizedBox(height: 20.h),
                  // 密码输入框
                  UnifiedInputField(
                    controller: _passwordController,
                    hintText: '请输入密码',
                    obscureText: !_isPasswordVisible,
                    prefixIcon: const Icon(Icons.lock_outline),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _isPasswordVisible
                            ? Icons.visibility
                            : Icons.visibility_off,
                      ),
                      onPressed: () {
                        setState(() {
                          _isPasswordVisible = !_isPasswordVisible;
                        });
                      },
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return '请输入密码';
                      }
                      if (value.length < 6) {
                        return '密码至少6位';
                      }
                      return null;
                    },
                  ),

                  SizedBox(height: 32.h),

                  // 登录按钮
                  UnifiedButton(
                    text: '登录',
                    onPressed: _handleLogin,
                    isLoading: _isLoading,
                    height: 50.h,
                  ),

                  SizedBox(height: 24.h),

                  // 其他登录方式
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '没有账号？',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      TextButton(
                        onPressed: () {
                          // 返回主页面进行手机号注册/登录
                          Get.back();
                        },
                        child: Text(
                          '一键登录',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Theme.of(context).colorScheme.primary,
                            
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 60.h),
                  // 底部提示
                  Text(
                    '登录即表示同意《用户协议》和《隐私政策》',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 处理登录
  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authService = AuthService();
      final result = await authService.passwordLogin(
        username: _usernameController.text.trim(),
        password: _passwordController.text.trim(),
      );

      if (result.success && result.data != null) {
        // 登录成功，保存用户名
        await _saveUsername(_usernameController.text.trim());

        // 调用AuthController的登录成功处理方法
        await Get.find<AuthController>().handleLoginSuccess(result.data!);
      } else {
        // 登录失败
        ShowToast.fail(result.message ?? '登录失败');
      }
    } catch (e) {
      ShowToast.fail('登录失败，请重试');
      print('登录异常: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
