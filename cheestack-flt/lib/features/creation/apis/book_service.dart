import 'dart:convert';
import 'dart:io';

import 'package:get/get.dart';
import 'package:cheestack_flt/apis/index.dart';
import 'package:cheestack_flt/features/auth/controllers/controller.dart';
import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/shared/utils/oxhttp/index.dart';
import 'package:cheestack_flt/shared/utils/index.dart';

/// 书籍服务类 - 本地优先架构
/// 优先使用本地数据服务，网络服务作为同步备用
class BookService {
  // 本地数据服务
  BookDataService? get _bookDataService =>
      Get.isRegistered<BookDataService>() ? BookDataService.to : null;

  /// 获取书籍列表 - 本地优先
  Future<List<BookModel>> getBookList({
    int skip = 0,
    int limit = 20,
    String? search,
    String? orderBy,
  }) async {
    try {
      // 优先使用本地数据服务
      if (_bookDataService != null) {
        Console.log('使用本地数据服务获取书籍列表');

        if (search != null && search.isNotEmpty) {
          return await _bookDataService!.searchBooks(
            search,
            offset: skip,
            limit: limit,
          );
        } else {
          return await _bookDataService!.getUserBooks(
            orderBy: orderBy ?? 'created_at DESC',
            offset: skip,
            limit: limit,
          );
        }
      } else {
        // 回退到网络API
        Console.log('本地数据服务不可用，使用网络API');
        return await _getBookListFromAPI(
          skip: skip,
          limit: limit,
          search: search,
          orderBy: orderBy,
        );
      }
    } catch (e) {
      Console.log('本地获取书籍列表失败，回退到网络API: $e');
      return await _getBookListFromAPI(
        skip: skip,
        limit: limit,
        search: search,
        orderBy: orderBy,
      );
    }
  }

  /// 从API获取书籍列表（回退方案）
  Future<List<BookModel>> _getBookListFromAPI({
    int skip = 0,
    int limit = 20,
    String? search,
    String? orderBy,
  }) async {
    Map<String, dynamic> filters = {
      "user_id": AuthController.to.usr.user?.id,
    };

    if (search != null && search.isNotEmpty) {
      filters['name__icontains'] = search;
    }

    final response = await OxHttp.to.get(
      HttpUrl.books,
      queryParameters: {
        "skip": skip,
        "limit": limit,
        "filters": jsonEncode(filters),
        if (orderBy != null) "order_by": orderBy,
      },
    );

    if (response.data != null) {
      return (response.data as List)
          .map<BookModel>((x) => BookModel.fromJson(x))
          .toList();
    }
    return [];
  }

  /// 创建书籍 - 本地优先
  Future<BookModel> createBook(
      Map<String, dynamic> bookData, String? coverPath) async {
    try {
      // 优先使用本地数据服务
      if (_bookDataService != null) {
        Console.log('使用本地数据服务创建书籍');

        final result = await _bookDataService!.createBook(
          name: bookData['name'],
          brief: bookData['brief'],
          cover: coverPath,
          privacy: bookData['privacy'],
        );

        if (result != null) {
          Console.log('书籍本地创建成功: ${result.name}');
          return result;
        } else {
          throw Exception('本地创建失败');
        }
      } else {
        // 回退到网络API
        Console.log('本地数据服务不可用，使用网络API');
        return await _createBookWithAPI(bookData, coverPath);
      }
    } catch (e) {
      Console.log('本地创建书籍失败，回退到网络API: $e');
      return await _createBookWithAPI(bookData, coverPath);
    }
  }

  /// 通过API创建书籍（回退方案）
  Future<BookModel> _createBookWithAPI(
      Map<String, dynamic> bookData, String? coverPath) async {
    // 处理封面上传
    if (coverPath != null && !coverPath.startsWith('http')) {
      final coverUrl = await _uploadCover(coverPath);
      bookData['cover'] = coverUrl;
    } else if (coverPath != null) {
      bookData['cover'] = coverPath;
    }

    // 添加用户ID
    bookData['user_id'] = AuthController.to.usr.user?.id;

    final response = await OxHttp.to.post(
      HttpUrl.books,
      data: bookData,
    );

    if (response.data != null) {
      return BookModel.fromJson(response.data);
    }
    throw Exception('创建书籍失败');
  }

  /// 更新书籍 - 本地优先
  Future<BookModel> updateBook(
      Map<String, dynamic> bookData, String? coverPath) async {
    final bookId = bookData['id'];
    if (bookId == null) throw Exception('书籍ID不能为空');

    try {
      // 优先使用本地数据服务
      if (_bookDataService != null) {
        Console.log('使用本地数据服务更新书籍');

        final success = await _bookDataService!.updateBook(
          bookId,
          name: bookData['name'],
          brief: bookData['brief'],
          cover: coverPath,
          privacy: bookData['privacy'],
        );

        if (success) {
          // 获取更新后的书籍数据
          final updatedBook = await _bookDataService!.getBookById(bookId);
          if (updatedBook != null) {
            Console.log('书籍本地更新成功: ${updatedBook.name}');
            return updatedBook;
          }
        }
        throw Exception('本地更新失败');
      } else {
        // 回退到网络API
        Console.log('本地数据服务不可用，使用网络API');
        return await _updateBookWithAPI(bookData, coverPath);
      }
    } catch (e) {
      Console.log('本地更新书籍失败，回退到网络API: $e');
      return await _updateBookWithAPI(bookData, coverPath);
    }
  }

  /// 通过API更新书籍（回退方案）
  Future<BookModel> _updateBookWithAPI(
      Map<String, dynamic> bookData, String? coverPath) async {
    final bookId = bookData['id'];

    // 处理封面上传
    if (coverPath != null && !coverPath.startsWith('http')) {
      final coverUrl = await _uploadCover(coverPath);
      bookData['cover'] = coverUrl;
    } else if (coverPath != null) {
      bookData['cover'] = coverPath;
    }

    // 移除ID，避免在请求体中重复
    bookData.remove('id');

    final response = await OxHttp.to.put(
      '${HttpUrl.books}/$bookId',
      data: bookData,
    );

    if (response.data != null) {
      return BookModel.fromJson(response.data);
    }
    throw Exception('更新书籍失败');
  }

  /// 删除书籍 - 本地优先
  Future<void> deleteBook(int bookId) async {
    try {
      // 优先使用本地数据服务
      if (_bookDataService != null) {
        Console.log('使用本地数据服务删除书籍');

        final success = await _bookDataService!.deleteBook(bookId);
        if (success) {
          Console.log('书籍本地删除成功');
          return;
        } else {
          throw Exception('本地删除失败');
        }
      } else {
        // 回退到网络API
        Console.log('本地数据服务不可用，使用网络API');
        await _deleteBookWithAPI(bookId);
      }
    } catch (e) {
      Console.log('本地删除书籍失败，回退到网络API: $e');
      await _deleteBookWithAPI(bookId);
    }
  }

  /// 通过API删除书籍（回退方案）
  Future<void> _deleteBookWithAPI(int bookId) async {
    await OxHttp.to.delete('${HttpUrl.books}/$bookId');
  }

  /// 获取书籍详情
  Future<BookModel> getBookDetail(int bookId) async {
    final response = await OxHttp.to.get('${HttpUrl.books}/$bookId');

    if (response.data != null) {
      return BookModel.fromJson(response.data);
    }
    throw Exception('获取书籍详情失败');
  }

  /// 获取书籍统计信息
  Future<Map<String, int>> getBookStats() async {
    Map<String, dynamic> filters = {
      "user_id": AuthController.to.usr.user?.id,
    };

    final response = await OxHttp.to.get(
      HttpUrl.books,
      queryParameters: {
        "skip": 0,
        "limit": 1000, // 获取所有书籍用于统计
        "filters": jsonEncode(filters),
      },
    );

    if (response.data != null) {
      final books = response.data as List;
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final weekStart = today.subtract(Duration(days: now.weekday - 1));

      int todayCount = 0;
      int weekCount = 0;

      for (var book in books) {
        final createdAt = DateTime.parse(book['created_at']);
        if (createdAt.isAfter(today)) {
          todayCount++;
        }
        if (createdAt.isAfter(weekStart)) {
          weekCount++;
        }
      }

      return {
        'total': books.length,
        'today': todayCount,
        'week': weekCount,
      };
    }
    return {'total': 0, 'today': 0, 'week': 0};
  }

  /// 上传封面图片
  Future<String> _uploadCover(String imagePath) async {
    try {
      final file = File(imagePath);
      if (!file.existsSync()) {
        throw Exception('图片文件不存在');
      }

      // 这里应该调用实际的文件上传API
      // 暂时返回本地路径，实际项目中需要上传到服务器
      // TODO: 实现文件上传功能
      // final response = await HttpUtils.to.uploadFile(
      //   HttpUrl.upload,
      //   file,
      //   fileName: 'book_cover_${DateTime.now().millisecondsSinceEpoch}.jpg',
      // );

      // if (response.data != null && response.data['url'] != null) {
      //   return response.data['url'];
      // }
      // throw Exception('上传失败');

      // 暂时返回本地路径
      return imagePath;
    } catch (e) {
      Console.log('封面上传失败: $e');
      // 如果上传失败，返回本地路径作为备选
      return imagePath;
    }
  }

  /// 搜索书籍
  Future<List<BookModel>> searchBooks(String keyword) async {
    return await getBookList(search: keyword, limit: 50);
  }

  /// 获取最近创建的书籍
  Future<List<BookModel>> getRecentBooks({int limit = 10}) async {
    return await getBookList(
      limit: limit,
      orderBy: '-created_at',
    );
  }
}
