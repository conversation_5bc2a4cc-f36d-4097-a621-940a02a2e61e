
import 'package:cheestack_flt/features/auth/controllers/controller.dart';
import 'package:cheestack_flt/models/index.dart';
import 'package:get/get.dart';
import 'card_api.dart';

/// 卡片服务类 - 本地优先架构
/// 优先使用本地数据服务，网络服务作为同步备用
class CardService {
  /// 获取CardAPI服务实例
  CardApiService get _cardApi => Get.find<CardApiService>();

  /// 获取本地数据服务实例
  CardDataService? get _cardDataService =>
      Get.isRegistered<CardDataService>() ? CardDataService.to : null;

  /// 获取卡片列表
  Future<List<CardModel>> getCardList({
    int skip = 0,
    int limit = 20,
    String? search,
    int? bookId,
    String? cardType,
    String? orderBy,
  }) async {
    // 优先使用本地数据服务
    if (_cardDataService != null) {
      if (bookId != null) {
        // 获取指定书籍的卡片
        return await _cardDataService!.getBookCards(
          bookId,
          orderBy: orderBy ?? 'created_at DESC',
          limit: limit,
          offset: skip,
        );
      } else if (search != null && search.isNotEmpty) {
        // 搜索卡片
        return await _cardDataService!.searchCards(
          search,
          bookId: bookId,
          limit: limit,
          offset: skip,
        );
      } else {
        // 获取所有卡片（通过书籍获取）
        // 这里需要先获取用户的书籍，然后获取所有卡片
        // 暂时返回空列表，实际使用中应该通过具体的bookId获取
        return [];
      }
    } else {
      // 回退到网络服务
      Map<String, dynamic> filters = {
        "user_id": AuthController.to.usr.user?.id,
      };

      if (search != null && search.isNotEmpty) {
        filters['title__icontains'] = search;
      }

      if (bookId != null) {
        filters['books'] = bookId;
      }

      if (cardType != null && cardType.isNotEmpty) {
        filters['card_type'] = cardType;
      }

      final response = await _cardApi.getCardList(
        skip: skip,
        limit: limit,
        filters: filters,
        orderBy: orderBy,
      );

      if (response.isSuccess) {
        return response.data!;
      }
      return [];
    }
  }

  /// 创建卡片
  Future<CardModel> createCard(CardModelCreate cardData) async {
    cardData.extra ??= {};

    // 优先使用本地数据服务
    if (_cardDataService != null && cardData.bookId != null) {
      final card = await _cardDataService!.createCard(
        bookId: cardData.bookId!,
        title: cardData.title ?? '',
        question: cardData.question ?? '',
        answer: cardData.answer ?? '',
        type: cardData.type,
        typeVersion: cardData.typeVersion,
        extra: cardData.extra,
        assets: cardData.cardAssets,
      );

      if (card != null) {
        return card;
      } else {
        throw Exception('创建卡片失败');
      }
    } else {
      // 回退到网络服务
      final response = await _cardApi.createCard(cardData: cardData);

      if (response.isSuccess) {
        return response.data!;
      }
      throw Exception('创建卡片失败: ${response.message}');
    }
  }

  /// 更新卡片
  Future<CardModel> updateCard(CardModelCreate cardData) async {
    if (cardData.id == null) throw Exception('卡片ID不能为空');

    // 优先使用本地数据服务
    if (_cardDataService != null) {
      final success = await _cardDataService!.updateCard(
        cardData.id!,
        title: cardData.title,
        question: cardData.question,
        answer: cardData.answer,
        extra: cardData.extra,
        assets: cardData.cardAssets,
      );

      if (success) {
        // 返回更新后的卡片
        final updatedCard = await _cardDataService!.getCardById(cardData.id!);
        if (updatedCard != null) {
          return updatedCard;
        }
      }
      throw Exception('更新卡片失败');
    } else {
      // 回退到网络服务
      final response = await _cardApi.updateCard(
        cardId: cardData.id!,
        cardData: cardData,
      );

      if (response.isSuccess) {
        return response.data!;
      }
      throw Exception('更新卡片失败: ${response.message}');
    }
  }

  /// 删除卡片
  Future<void> deleteCard(int cardId) async {
    // 优先使用本地数据服务
    if (_cardDataService != null) {
      final success = await _cardDataService!.deleteCard(cardId);
      if (!success) {
        throw Exception('删除卡片失败');
      }
    } else {
      // 回退到网络服务
      final response = await _cardApi.deleteCard(cardId);
      if (!response.isSuccess) {
        throw Exception('删除卡片失败: ${response.message}');
      }
    }
  }

  /// 获取卡片详情
  Future<CardModel> getCardDetail(int cardId) async {
    final response = await _cardApi.getCardDetail(cardId);

    if (response.isSuccess) {
      return response.data!;
    }
    throw Exception('获取卡片详情失败: ${response.message}');
  }

  /// 获取卡片统计信息
  Future<Map<String, int>> getCardStats() async {
    final userId = AuthController.to.usr.user?.id;
    if (userId == null) {
      return {'total': 0, 'today': 0, 'week': 0};
    }

    final response = await _cardApi.getCardStats(userId: int.parse(userId));

    if (response.isSuccess) {
      return response.data!;
    }
    return {'total': 0, 'today': 0, 'week': 0};
  }

  /// 搜索卡片
  Future<List<CardModel>> searchCards(String keyword) async {
    final response = await _cardApi.searchCards(keyword: keyword, limit: 50);
    if (response.isSuccess) {
      return response.data!;
    }
    return [];
  }

  /// 获取最近创建的卡片
  Future<List<CardModel>> getRecentCards({int limit = 10}) async {
    final userId = AuthController.to.usr.user?.id;
    if (userId == null) return [];

    final response = await _cardApi.getRecentCards(
      userId: int.parse(userId),
      limit: limit,
    );
    if (response.isSuccess) {
      return response.data!;
    }
    return [];
  }

  /// 批量删除卡片
  Future<void> batchDeleteCards(List<int> cardIds) async {
    final response = await _cardApi.batchDeleteCards(cardIds: cardIds);
    if (!response.isSuccess) {
      throw Exception('批量删除卡片失败: ${response.message}');
    }
  }

  /// 将卡片添加到书籍
  Future<void> addCardToBook(int cardId, int bookId) async {
    // 这里需要根据实际API实现
    // 暂时使用更新卡片的方式
    final card = await getCardDetail(cardId);
    final cardData = CardModelCreate(
      id: card.id,
      bookId: bookId,
      type: card.type,
      typeVersion: card.typeVersion,
      title: card.title,
      question: card.question,
      answer: card.answer,
      extra: card.extra as Map<String, dynamic>?,
      cardAssets: card.cardAssets ?? [],
    );

    await updateCard(cardData);
  }

  /// 从书籍中移除卡片
  Future<void> removeCardFromBook(int cardId) async {
    final card = await getCardDetail(cardId);
    final cardData = CardModelCreate(
      id: card.id,
      bookId: null, // 移除书籍关联
      type: card.type,
      typeVersion: card.typeVersion,
      title: card.title,
      question: card.question,
      answer: card.answer,
      extra: card.extra as Map<String, dynamic>?,
      cardAssets: card.cardAssets ?? [],
    );

    await updateCard(cardData);
  }
}
