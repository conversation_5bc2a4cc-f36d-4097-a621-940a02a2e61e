import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../widgets/index.dart';
import '../controllers/book_detail_controller.dart';

/// 书籍详情页面
class BookDetailPage extends StatefulWidget {
  const BookDetailPage({Key? key}) : super(key: key);

  @override
  State<BookDetailPage> createState() => _BookDetailPageState();
}

class _BookDetailPageState extends State<BookDetailPage>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return const _BookDetailView();
  }
}

class _BookDetailView extends GetView<BookDetailController> {
  const _BookDetailView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<BookDetailController>(
      init: BookDetailController(),
      builder: (_) {
        if (controller.hasError) {
          return _buildErrorView();
        }

        return Scaffold(
          appBar: _buildAppBar(),
          body: controller.isLoading ? _buildLoadingView() : _buildView(),
        );
      },
    );
  }

  /// 错误视图
  Widget _buildErrorView() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('书籍详情'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64.sp,
              color: Theme.of(Get.context!).colorScheme.error,
            ),
            SizedBox(height: 16.h),
            Text(
              controller.errorMessage,

              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24.h),
            ElevatedButton(
              onPressed: () => Get.back(),
              child: const Text('返回'),
            ),
          ],
        ),
      ),
    );
  }

  /// 加载视图
  Widget _buildLoadingView() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  /// 主视图
  Widget _buildView() {
    return RefreshIndicator(
      onRefresh: controller.onRefresh,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildBookHeader(),
            SizedBox(height: 16.h),
            _buildStatisticsCards(),
            SizedBox(height: 16.h),
            _buildBookBrief(),
            SizedBox(height: 16.h),
            _buildRecentCards(),
            SizedBox(height: 16.h),
            _buildActionButtons(),
            SizedBox(height: 32.h), // 底部留白
          ],
        ),
      ),
    );
  }

  /// 构建AppBar
  AppBar _buildAppBar() {
    return AppBar(
      title: const Text('书籍详情'),
      centerTitle: true,
      actions: [
        IconButton(
          icon: const Icon(Icons.edit),
          onPressed: controller.editBook,
          tooltip: '编辑',
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) {
            switch (value) {
              case 'share':
                controller.shareBook();
                break;
              case 'favorite':
                controller.toggleFavorite();
                break;
              case 'feedback':
                controller.toFeedback();
                break;
              case 'delete':
                controller.deleteBook();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'share',
              child: ListTile(
                leading: Icon(Icons.share),
                title: Text('分享'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'favorite',
              child: ListTile(
                leading: Icon(Icons.favorite_border),
                title: Text('收藏'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'feedback',
              child: ListTile(
                leading: Icon(Icons.feedback),
                title: Text('反馈'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            PopupMenuItem(
              value: 'delete',
              child: ListTile(
                leading: Icon(Icons.delete,
                    color: Theme.of(Get.context!).colorScheme.error),
                title: Text('删除',
                    style: TextStyle(
                        color: Theme.of(Get.context!).colorScheme.error)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建书籍头部信息
  Widget _buildBookHeader() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildBookCover(),
            SizedBox(width: 16.w),
            Expanded(child: _buildBookInfo()),
          ],
        ),
      ),
    );
  }

  /// 构建书籍封面
  Widget _buildBookCover() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8.r),
      child: Container(
        width: 80.w,
        height: 110.h,
        decoration: BoxDecoration(
          color: Theme.of(Get.context!).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: controller.bookModel.cover?.isNotEmpty == true
            ? Simage(
                url: controller.bookModel.cover!,
                fit: BoxFit.cover,
                width: 80.w,
                height: 110.h,
              )
            : Icon(
                Icons.book,
                size: 40.sp,
                color: Theme.of(Get.context!).colorScheme.onSurfaceVariant,
              ),
      ),
    );
  }

  /// 构建书籍信息
  Widget _buildBookInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          controller.bookModel.name ?? '未命名书籍',
          
          maxLines: 2,
        ),
        SizedBox(height: 8.h),
        Row(
          children: [
            Icon(
              controller.privacyIcon,
              size: 16.sp,
              color: Theme.of(Get.context!).colorScheme.onSurfaceVariant,
            ),
            SizedBox(width: 4.w),
            Text(
              controller.privacyText,
              
            ),
          ],
        ),
        SizedBox(height: 4.h),
        if (controller.bookModel.user?.username != null) ...[
          Row(
            children: [
              Icon(
                Icons.person,
                size: 16.sp,
                color: Theme.of(Get.context!).colorScheme.onSurfaceVariant,
              ),
              SizedBox(width: 4.w),
              Text(
                controller.bookModel.user!.username!,
                style: TextStyle(fontSize: 14.sp, color: Theme.of(Get.context!))
                    .colorScheme
                    .primary,
              ),
            ],
          ),
          SizedBox(height: 4.h),
        ],
        Row(
          children: [
            Icon(
              Icons.access_time,
              size: 16.sp,
              color: Theme.of(Get.context!).colorScheme.onSurfaceVariant,
            ),
            SizedBox(width: 4.w),
            Text(
              '创建于 ${_formatTime(controller.bookModel.createdAt)}',
              fontSize: 12.sp,
              color: Theme.of(Get.context!).colorScheme.onSurfaceVariant,
            ),
          ],
        ),
        if (controller.bookModel.updatedAt !=
            controller.bookModel.createdAt) ...[
          SizedBox(height: 2.h),
          Row(
            children: [
              Icon(
                Icons.edit,
                size: 16.sp,
                color: Theme.of(Get.context!).colorScheme.onSurfaceVariant,
              ),
              SizedBox(width: 4.w),
              Text(
                '更新于 ${_formatTime(controller.bookModel.updatedAt)}',
                fontSize: 12.sp,
                color: Theme.of(Get.context!).colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// 构建统计信息卡片
  Widget _buildStatisticsCards() {
    return Row(
      children: [
        Expanded(
            child: _buildStatCard('总卡片', '${controller.totalCards}',
                Icons.credit_card, Theme.of(Get.context!).colorScheme.primary)),
        SizedBox(width: 6.w),
        Expanded(
            child: _buildStatCard(
                '已学习',
                '${controller.studiedCards}',
                Icons.check_circle,
                Theme.of(Get.context!).colorScheme.secondary)),
        SizedBox(width: 6.w),
        Expanded(
            child: _buildStatCard('今日学习', '${controller.todayStudied}',
                Icons.today, Theme.of(Get.context!).colorScheme.tertiary)),
        SizedBox(width: 6.w),
        Expanded(
            child: _buildStatCard('完成率', controller.progressText,
                Icons.trending_up, Theme.of(Get.context!).colorScheme.error)),
      ],
    );
  }

  /// 构建单个统计卡片
  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 12.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 18.sp),
            SizedBox(height: 4.h),
            Text(
              value,
              fontSize: 15.sp,
              
              color: color,
            ),
            SizedBox(height: 2.h),
            Text(
              title,
              style: TextStyle(fontSize: 11.sp, color: Theme.of(Get.context!))
                  .colorScheme
                  .onSurfaceVariant,
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建书籍简介
  Widget _buildBookBrief() {
    final brief = controller.bookModel.brief;
    if (brief == null || brief.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.description,
                  size: 20.sp,
                  color: Theme.of(Get.context!).colorScheme.primary,
                ),
                SizedBox(width: 8.w),
                Text(
                  '书籍简介',
                  fontSize: 16.sp,
                  
                ),
                const Spacer(),
                if (brief.length > 100)
                  GestureDetector(
                    onTap: controller.toggleBriefExpansion,
                    child: Text(
                      controller.isBriefExpanded ? '收起' : '展开',
                      style: TextStyle(
                              fontSize: 14.sp, color: Theme.of(Get.context!))
                          .colorScheme
                          .primary,
                    ),
                  ),
              ],
            ),
            SizedBox(height: 12.h),
            Text(
              brief,
              style: TextStyle(fontSize: 14.sp, color: Theme.of(Get.context!))
                  .colorScheme
                  .onSurfaceVariant,
              maxLines: controller.isBriefExpanded ? null : 3,
              overflow:
                  controller.isBriefExpanded ? null : TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建最近卡片
  Widget _buildRecentCards() {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.history,
                  size: 20.sp,
                  color: Theme.of(Get.context!).colorScheme.primary,
                ),
                SizedBox(width: 8.w),
                Text(
                  '最近卡片',
                  fontSize: 16.sp,
                  
                ),
                const Spacer(),
                TextButton(
                  onPressed: controller.manageCards,
                  child: const Text('查看全部'),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            if (controller.recentCards.isEmpty)
              Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 20.h),
                  child: Column(
                    children: [
                      Icon(
                        Icons.credit_card_off,
                        size: 48.sp,
                        color:
                            Theme.of(Get.context!).colorScheme.onSurfaceVariant,
                      ),
                      SizedBox(height: 8.h),
                      Text(
                        '还没有卡片',
                        style: TextStyle(color: Theme.of(Get.context!))
                            .colorScheme
                            .onSurfaceVariant,
                      ),
                    ],
                  ),
                ),
              )
            else
              // TODO: 实现卡片列表
              const Text('卡片列表功能开发中...'),
          ],
        ),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: controller.startStudy,
                icon: const Icon(Icons.play_arrow),
                label: const Text('开始学习'),
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: 12.h),
                ),
              ),
            ),
            // 只有作者才能看到管理卡片按钮
            if (controller.isAuthor) ...[
              SizedBox(width: 12.w),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: controller.manageCards,
                  icon: const Icon(Icons.credit_card),
                  label: const Text('管理卡片'),
                  style: OutlinedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 12.h),
                  ),
                ),
              ),
            ],
          ],
        ),
        SizedBox(height: 12.h),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: controller.setBookSchedule,
            icon: const Icon(Icons.schedule),
            label: const Text('制定学习计划'),
            style: OutlinedButton.styleFrom(
              padding: EdgeInsets.symmetric(vertical: 12.h),
            ),
          ),
        ),
      ],
    );
  }

  /// 格式化时间显示
  String _formatTime(String? timeStr) {
    if (timeStr == null) return '未知';
    try {
      final time = DateTime.parse(timeStr);
      return controller.formatTime(time);
    } catch (e) {
      return '未知';
    }
  }
}
