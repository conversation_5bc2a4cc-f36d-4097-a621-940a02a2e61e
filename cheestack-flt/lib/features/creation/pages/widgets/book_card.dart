import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:cheestack_flt/models/index.dart';
import '../../controllers/creation_controller.dart';

/// 书籍卡片组件 - 列表视图
class BookListCard extends StatelessWidget {
  final BookModel book;
  final CreationController controller;
  final VoidCallback? onTap;

  const BookListCard({
    super.key,
    required this.book,
    required this.controller,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.only(bottom: AppTheme.spacingSmall),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: onTap ?? () => controller.viewBookDetail(book),
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: AppTheme.paddingMedium,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildCover(context),
              SizedBox(width: 12.w),
              Expanded(child: _buildContent(context)),
              _buildActions(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCover(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8.r),
      child: Container(
        width: 60.w,
        height: 80.h,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: book.cover?.isNotEmpty == true
            ? Simage(
                url: book.cover!,
                fit: BoxFit.cover,
                width: 60.w,
                height: 80.h,
              )
            : Icon(
                Icons.book,
                size: 32.sp,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                book.name ?? '未命名书籍',
                style: TextStyle(),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            _buildPrivacyBadge(context),
          ],
        ),
        SizedBox(height: 4.h),
        if (book.brief?.isNotEmpty == true) ...[
          Text(
            book.brief!,
            style: TextStyle(
              fontSize: AppTheme.fontSmall,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(height: 8.h),
        ],
        _buildMetaInfo(context),
      ],
    );
  }

  Widget _buildPrivacyBadge(BuildContext context) {
    final isPublic = book.privacy == 'public';
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: isPublic
            ? Theme.of(context).colorScheme.primaryContainer
            : Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isPublic ? Icons.public : Icons.lock,
            size: 12.sp,
            color: isPublic
                ? Theme.of(context).colorScheme.onPrimaryContainer
                : Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          SizedBox(width: 2.w),
          Text(
            isPublic ? '公开' : '私有',
            style: TextStyle(
              fontSize: 10.sp,
              color: isPublic
                  ? Theme.of(context).colorScheme.onPrimaryContainer
                  : Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetaInfo(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.access_time,
          size: 14.sp,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        SizedBox(width: 4.w),
        Text(
          _formatTime(book.createdAt),
          style: TextStyle(
            fontSize: AppTheme.fontSmall,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        if (book.updatedAt != book.createdAt) ...[
          SizedBox(width: 12.w),
          Icon(
            Icons.edit,
            size: 14.sp,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          SizedBox(width: 4.w),
          Text(
            _formatTime(book.updatedAt),
            style: TextStyle(
              fontSize: AppTheme.fontSmall,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildActions(BuildContext context) {
    return PopupMenuButton<String>(
      icon: Icon(
        Icons.more_vert,
        color: Theme.of(context).colorScheme.onSurfaceVariant,
        size: 20.sp,
      ),
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'edit',
          child: Row(
            children: [
              Icon(
                Icons.edit,
                size: 16.sp,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              SizedBox(width: 8.w),
              const Text('编辑'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'cards',
          child: Row(
            children: [
              Icon(
                Icons.credit_card,
                size: 16.sp,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              SizedBox(width: 8.w),
              const Text('管理卡片'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'duplicate',
          child: Row(
            children: [
              Icon(
                Icons.copy,
                size: 16.sp,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              SizedBox(width: 8.w),
              const Text('复制'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Icon(
                Icons.delete,
                size: 16.sp,
                color: Theme.of(context).colorScheme.error,
              ),
              SizedBox(width: 8.w),
              Text(
                '删除',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.error,
                ),
              ),
            ],
          ),
        ),
      ],
      onSelected: (value) {
        switch (value) {
          case 'edit':
            controller.editBook(book);
            break;
          case 'cards':
            controller.manageBookCards(book);
            break;
          case 'duplicate':
            controller.duplicateBook(book);
            break;
          case 'delete':
            controller.deleteBook(book);
            break;
        }
      },
    );
  }

  String _formatTime(String? timeStr) {
    if (timeStr == null) return '未知';
    try {
      final time = DateTime.parse(timeStr);
      final now = DateTime.now();
      final diff = now.difference(time);

      if (diff.inDays > 0) {
        return '${diff.inDays}天前';
      } else if (diff.inHours > 0) {
        return '${diff.inHours}小时前';
      } else if (diff.inMinutes > 0) {
        return '${diff.inMinutes}分钟前';
      } else {
        return '刚刚';
      }
    } catch (e) {
      return '未知';
    }
  }
}

/// 书籍卡片组件 - 网格视图
class BookGridCard extends StatelessWidget {
  final BookModel book;
  final CreationController controller;
  final VoidCallback? onTap;

  const BookGridCard({
    super.key,
    required this.book,
    required this.controller,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: onTap ?? () => controller.viewBookDetail(book),
        borderRadius: BorderRadius.circular(12.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCover(context),
            Expanded(child: _buildContent(context)),
          ],
        ),
      ),
    );
  }

  Widget _buildCover(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 120.h,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.vertical(top: Radius.circular(12.r)),
      ),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.vertical(top: Radius.circular(12.r)),
            child: book.cover?.isNotEmpty == true
                ? Simage(
                    url: book.cover!,
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: 120.h,
                  )
                : Center(
                    child: Icon(
                      Icons.book,
                      size: 48.sp,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
          ),
          Positioned(
            top: 8.h,
            right: 8.w,
            child: _buildPrivacyBadge(context),
          ),
          Positioned(
            top: 8.h,
            left: 8.w,
            child: _buildActions(context),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(12.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            book.name ?? '未命名书籍',
            style: TextStyle(),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          if (book.brief?.isNotEmpty == true) ...[
            SizedBox(height: 4.h),
            Text(
              book.brief!,
              style: TextStyle(
                fontSize: AppTheme.fontSmall,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
          const Spacer(),
          _buildMetaInfo(context),
        ],
      ),
    );
  }

  Widget _buildPrivacyBadge(BuildContext context) {
    final isPublic = book.privacy == 'public';
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Icon(
        isPublic ? Icons.public : Icons.lock,
        size: 16.sp,
        color: isPublic
            ? Theme.of(context).colorScheme.primary
            : Theme.of(context).colorScheme.onSurfaceVariant,
      ),
    );
  }

  Widget _buildActions(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: PopupMenuButton<String>(
        icon: Icon(
          Icons.more_vert,
          color: Theme.of(context).colorScheme.onSurface,
          size: 16.sp,
        ),
        itemBuilder: (context) => [
          PopupMenuItem(
            value: 'edit',
            child: Row(
              children: [
                Icon(
                  Icons.edit,
                  size: 16.sp,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                SizedBox(width: 8.w),
                const Text('编辑'),
              ],
            ),
          ),
          PopupMenuItem(
            value: 'cards',
            child: Row(
              children: [
                Icon(
                  Icons.credit_card,
                  size: 16.sp,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                SizedBox(width: 8.w),
                const Text('管理卡片'),
              ],
            ),
          ),
          PopupMenuItem(
            value: 'duplicate',
            child: Row(
              children: [
                Icon(
                  Icons.copy,
                  size: 16.sp,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                SizedBox(width: 8.w),
                const Text('复制'),
              ],
            ),
          ),
          PopupMenuItem(
            value: 'delete',
            child: Row(
              children: [
                Icon(
                  Icons.delete,
                  size: 16.sp,
                  color: Theme.of(context).colorScheme.error,
                ),
                SizedBox(width: 8.w),
                Text(
                  '删除',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.error,
                  ),
                ),
              ],
            ),
          ),
        ],
        onSelected: (value) {
          switch (value) {
            case 'edit':
              controller.editBook(book);
              break;
            case 'cards':
              controller.manageBookCards(book);
              break;
            case 'duplicate':
              controller.duplicateBook(book);
              break;
            case 'delete':
              controller.deleteBook(book);
              break;
          }
        },
      ),
    );
  }

  Widget _buildMetaInfo(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.access_time,
          size: 12.sp,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        SizedBox(width: 4.w),
        Expanded(
          child: Text(
            _formatTime(book.createdAt),
            style: TextStyle(
              fontSize: 10.sp,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ),
      ],
    );
  }

  String _formatTime(String? timeStr) {
    if (timeStr == null) return '未知';
    try {
      final time = DateTime.parse(timeStr);
      final now = DateTime.now();
      final diff = now.difference(time);

      if (diff.inDays > 0) {
        return '${diff.inDays}天前';
      } else if (diff.inHours > 0) {
        return '${diff.inHours}小时前';
      } else if (diff.inMinutes > 0) {
        return '${diff.inMinutes}分钟前';
      } else {
        return '刚刚';
      }
    } catch (e) {
      return '未知';
    }
  }
}
