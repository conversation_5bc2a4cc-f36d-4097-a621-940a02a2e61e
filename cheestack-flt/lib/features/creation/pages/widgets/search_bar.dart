import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';


/// 创作模块通用搜索栏
class CreationSearchBar extends StatefulWidget {
  final String hintText;
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;
  final TextEditingController? controller;
  final Widget? prefixIcon;
  final Widget? suffixIcon;

  const CreationSearchBar({
    super.key,
    required this.hintText,
    this.onChanged,
    this.onSubmitted,
    this.controller,
    this.prefixIcon,
    this.suffixIcon,
  });

  @override
  State<CreationSearchBar> createState() => _CreationSearchBarState();
}

class _CreationSearchBarState extends State<CreationSearchBar> {
  late TextEditingController _controller;
  bool _showClearButton = false;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    } else {
      _controller.removeListener(_onTextChanged);
    }
    super.dispose();
  }

  void _onTextChanged() {
    final hasText = _controller.text.isNotEmpty;
    if (hasText != _showClearButton) {
      setState(() {
        _showClearButton = hasText;
      });
    }
    widget.onChanged?.call(_controller.text);
  }

  void _clearText() {
    _controller.clear();
    widget.onChanged?.call('');
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 48.h,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(24.r),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: TextField(
        controller: _controller,
        onSubmitted: widget.onSubmitted,
        decoration: InputDecoration(
          hintText: widget.hintText,
          hintStyle: TextStyle(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            fontSize: AppTheme.fontSmall,
          ),
          prefixIcon: widget.prefixIcon ??
              Icon(
                Icons.search,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                size: 20.sp,
              ),
          suffixIcon: _showClearButton
              ? IconButton(
                  icon: Icon(
                    Icons.clear,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    size: 20.sp,
                  ),
                  onPressed: _clearText,
                )
              : widget.suffixIcon,
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: 16.w,
            vertical: 12.h,
          ),
        ),
        style: TextStyle(
          fontSize: AppTheme.fontSmall,
          color: Theme.of(context).colorScheme.onSurface,
        ),
      ),
    );
  }
}

/// 带过滤器的搜索栏
class CreationSearchBarWithFilter extends StatelessWidget {
  final String hintText;
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;
  final VoidCallback? onFilterTap;
  final TextEditingController? controller;
  final bool hasActiveFilter;

  const CreationSearchBarWithFilter({
    super.key,
    required this.hintText,
    this.onChanged,
    this.onSubmitted,
    this.onFilterTap,
    this.controller,
    this.hasActiveFilter = false,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: CreationSearchBar(
            hintText: hintText,
            onChanged: onChanged,
            onSubmitted: onSubmitted,
            controller: controller,
          ),
        ),
        if (onFilterTap != null) ...[
          SizedBox(width: 12.w),
          GestureDetector(
            onTap: onFilterTap,
            child: Container(
              width: 48.w,
              height: 48.h,
              decoration: BoxDecoration(
                color: hasActiveFilter
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(24.r),
                border: Border.all(
                  color: hasActiveFilter
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                ),
              ),
              child: Icon(
                Icons.tune,
                color: hasActiveFilter
                    ? Theme.of(context).colorScheme.onPrimary
                    : Theme.of(context).colorScheme.onSurfaceVariant,
                size: 20.sp,
              ),
            ),
          ),
        ],
      ],
    );
  }
}

/// 搜索建议列表
class SearchSuggestionList extends StatelessWidget {
  final List<String> suggestions;
  final Function(String) onSuggestionTap;
  final Function(String)? onSuggestionRemove;

  const SearchSuggestionList({
    super.key,
    required this.suggestions,
    required this.onSuggestionTap,
    this.onSuggestionRemove,
  });

  @override
  Widget build(BuildContext context) {
    if (suggestions.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: EdgeInsets.only(top: 8.h),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: suggestions.length,
        separatorBuilder: (context, index) => Divider(
          height: 1.h,
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
        itemBuilder: (context, index) {
          final suggestion = suggestions[index];
          return ListTile(
            dense: true,
            leading: Icon(
              Icons.history,
              size: 16.sp,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            title: Text(
              suggestion,
              style: TextStyle(fontSize: AppTheme.fontSmall),
            ),
            trailing: onSuggestionRemove != null
                ? IconButton(
                    icon: Icon(
                      Icons.close,
                      size: 16.sp,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    onPressed: () => onSuggestionRemove!(suggestion),
                  )
                : null,
            onTap: () => onSuggestionTap(suggestion),
          );
        },
      ),
    );
  }
}
