import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:cheestack_flt/apis/index.dart';
import 'package:cheestack_flt/common/enums/index.dart';
import 'package:cheestack_flt/features/auth/controllers/controller.dart';
import 'package:cheestack_flt/shared/extensions/index.dart';
import 'package:cheestack_flt/services/index.dart';

import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/routes/index.dart';
import 'package:cheestack_flt/shared/utils/oxhttp/index.dart';
import 'package:cheestack_flt/shared/utils/index.dart';
import 'package:cheestack_flt/shared/utils/error_handler.dart';
import 'package:cheestack_flt/widgets/index.dart';
import '../apis/book_service.dart';
import '../apis/card_service.dart';
import '../services/sync_manager.dart';
import '../services/ux_manager.dart';

/// 同步状态枚举
enum SyncStatus { idle, syncing, success, failed }

/// 同步状态数据类
class SyncState {
  final SyncStatus status;
  final double progress;
  final String? message;
  final DateTime? lastSyncTime;

  const SyncState({
    required this.status,
    this.progress = 0.0,
    this.message,
    this.lastSyncTime,
  });

  SyncState copyWith({
    SyncStatus? status,
    double? progress,
    String? message,
    DateTime? lastSyncTime,
  }) {
    return SyncState(
      status: status ?? this.status,
      progress: progress ?? this.progress,
      message: message ?? this.message,
      lastSyncTime: lastSyncTime ?? this.lastSyncTime,
    );
  }
}

/// 创作模块主控制器 - 本地优先架构
class CreationController extends GetxController {
  // === 数据服务层 ===
  // 本地数据服务优先，网络服务作为同步备用
  BookDataService? get _bookDataService =>
      Get.isRegistered<BookDataService>() ? BookDataService.to : null;
  CardDataService? get _cardDataService =>
      Get.isRegistered<CardDataService>() ? CardDataService.to : null;
  SyncManager? get _syncManager =>
      Get.isRegistered<SyncManager>() ? SyncManager.to : null;
  UXManager? get _uxManager =>
      Get.isRegistered<UXManager>() ? UXManager.to : null;

  // 网络服务（仅用于同步）
  final BookService _bookService = BookService();
  final CardService _cardService = CardService();

  // === 状态变量 ===
  /// 页面加载状态
  bool isLoading = false;

  /// 创作统计数据
  int totalBooks = 0;
  int totalCards = 0;
  int todayCreations = 0;
  int weekCreations = 0;

  /// 最近创作列表
  List<Map<String, dynamic>> recentCreations = [];

  /// 书籍管理相关
  List<BookModel> bookList = [];
  List<BookModel> filteredBookList = [];
  String bookSearchKeyword = '';

  /// 书籍筛选和排序相关
  String selectedPrivacyFilter = 'all'; // all, public, private
  String selectedTimeFilter = 'all'; // all, today, week, month
  String selectedSortOption =
      'created_desc'; // created_desc, created_asc, updated_desc, updated_asc, name_asc, name_desc
  bool isFilterActive = false;
  bool isGridView = false; // 列表/网格视图切换

  /// 卡片管理相关
  List<CardModel> cardList = [];
  String cardSearchKeyword = '';

  /// 同步状态管理 - 使用新的SyncState类
  SyncState _syncState = const SyncState(status: SyncStatus.idle);
  SyncState get syncState => _syncState;

  /// 兼容性属性（保持向后兼容）
  bool get isSyncing => _syncState.status == SyncStatus.syncing;
  String get syncStatus => _syncState.status.name;
  double get syncProgress => _syncState.progress;
  String? get syncErrorMessage => _syncState.message;

  /// 输入框控制器
  TextEditingController textEditingController = TextEditingController();

  /// 输入框内容改变回调
  Function(String)? onInputValueChanged;

  CreationController();

  Future init() async {
    await loadCreationStats();
    update();
  }

  void onTap() {}

  /// 加载创作统计数据 - 本地优先
  Future<void> loadCreationStats() async {
    isLoading = true;
    update();

    try {
      await Future.wait([
        _loadCreationStats(),
        _loadRecentCreations(),
        loadBookList(),
      ]);
    } catch (e) {
      Console.log('加载创作统计失败: $e');
      if (_uxManager != null) {
        _uxManager!.showError(e, context: 'load_data');
      } else {
        final friendlyMessage = ErrorHandler.getUserFriendlyMessage(e);
        ShowToast.fail(friendlyMessage);
      }
    } finally {
      isLoading = false;
      update();
    }
  }

  @override
  void onInit() {
    super.onInit();

    // 延迟执行测试
    Future.delayed(const Duration(seconds: 5), () {
      _testBookDataService();
    });
  }

  /// 测试BookDataService功能
  Future<void> _testBookDataService() async {
    Console.log('=== 开始测试BookDataService功能 ===');

    try {
      // 检查服务是否可用
      Console.log(
          '检查BookDataService是否注册: ${Get.isRegistered<BookDataService>()}');
      Console.log(
          '检查UserDataService是否注册: ${Get.isRegistered<UserDataService>()}');

      if (!Get.isRegistered<BookDataService>()) {
        Console.log('❌ BookDataService未注册');
        return;
      }

      final bookDataService = BookDataService.to;
      final userDataService = UserDataService.to;

      Console.log('当前用户: ${userDataService.currentUser?.username}');
      Console.log('用户ID: ${userDataService.currentUser?.id}');

      if (userDataService.currentUser == null) {
        Console.log('❌ 用户未登录，无法测试');
        return;
      }

      // 测试创建书籍
      Console.log('开始测试创建书籍...');
      final result = await bookDataService.createBook(
        name: '自动测试书籍_${DateTime.now().millisecondsSinceEpoch}',
        brief: '这是一个自动测试创建的书籍',
        privacy: 'private',
      );

      if (result != null) {
        Console.log('✅ 书籍创建成功: ${result.name} (ID: ${result.id})');
        ShowToast.success('测试成功：书籍创建完成');
      } else {
        Console.log('❌ 书籍创建失败');
        ShowToast.fail('测试失败：书籍创建失败');
      }
    } catch (e, stackTrace) {
      Console.log('❌ 测试过程中发生错误: $e');
      Console.log('错误堆栈: $stackTrace');
      final friendlyMessage = ErrorHandler.getUserFriendlyMessage(e);
      ShowToast.fail(friendlyMessage);
    }

    Console.log('=== BookDataService功能测试完成 ===');
  }

  @override
  void onReady() async {
    super.onReady();
    await loadCreationStats();
  }

  /// 加载创作统计数据 - 本地优先
  Future<void> _loadCreationStats() async {
    try {
      // 优先使用本地数据服务计算统计
      if (_bookDataService != null) {
        Console.log('使用本地数据服务计算统计');

        // 获取本地书籍数量
        final books = await _bookDataService!.getUserBooks();
        totalBooks = books.length;

        // 获取卡片数量（通过书籍获取）
        int cardCount = 0;
        if (_cardDataService != null) {
          for (final book in books) {
            if (book.id != null) {
              final bookCards = await _cardDataService!.getBookCards(book.id!);
              cardCount += bookCards.length;
            }
          }
        }
        totalCards = cardCount;

        // 计算今日和本周创作数量
        final now = DateTime.now();
        final today = DateTime(now.year, now.month, now.day);
        final weekStart = now.subtract(Duration(days: now.weekday - 1));
        final weekStartDay =
            DateTime(weekStart.year, weekStart.month, weekStart.day);

        // 计算今日创作的书籍数量
        final todayBooks = books.where((book) {
          if (book.createdAt == null) return false;
          final createdAt = DateTime.parse(book.createdAt!);
          return createdAt.isAfter(today);
        }).length;

        // 计算本周创作的书籍数量
        final weekBooks = books.where((book) {
          if (book.createdAt == null) return false;
          final createdAt = DateTime.parse(book.createdAt!);
          return createdAt.isAfter(weekStartDay);
        }).length;

        // 暂时只统计书籍的创作数量，卡片统计较复杂
        todayCreations = todayBooks;
        weekCreations = weekBooks;

        Console.log(
            '📊 本地统计: 书籍=${totalBooks}, 卡片=${totalCards}, 今日=${todayCreations}, 本周=${weekCreations}');
      } else {
        // 回退到网络API
        Console.log('本地数据服务不可用，使用网络API');
        await _loadStatsFromAPI();
      }
    } catch (e) {
      Console.log('本地统计计算失败，回退到网络API: $e');
      await _loadStatsFromAPI();
    }
  }

  /// 从API加载统计数据（回退方案）
  Future<void> _loadStatsFromAPI() async {
    await asyncRequest(
      () => OxHttp.to.get(HttpUrl.creationStats),
      onSuccess: (data) {
        Console.log('📊 从API加载创作统计数据成功: $data');

        totalBooks = data['total_books'] ?? 0;
        totalCards = data['total_cards'] ?? 0;
        todayCreations = data['today_creations'] ?? 0;
        weekCreations = data['week_creations'] ?? 0;

        Console.log(
            '📊 API统计: 书籍=${totalBooks}, 卡片=${totalCards}, 今日=${todayCreations}, 本周=${weekCreations}');
      },
      onFailure: (error) {
        final friendlyMessage = ErrorHandler.getUserFriendlyMessage(error);
        Console.log('❌ 加载创作统计失败: $friendlyMessage');
        // 不显示错误提示，因为这是回退方案
      },
    );
  }

  /// 加载最近创作数据 - 本地优先，显示最近3条
  Future<void> _loadRecentCreations() async {
    recentCreations.clear();

    try {
      // 优先使用本地数据服务
      if (_bookDataService != null && _cardDataService != null) {
        Console.log('使用本地数据服务加载最近创作');

        // 获取最近的书籍和卡片
        final books = await _bookDataService!.getUserBooks(
          orderBy: 'created_at DESC',
          limit: 5,
        );

        // 收集所有创作项目（书籍和卡片）
        List<Map<String, dynamic>> allCreations = [];

        // 添加书籍
        for (final book in books) {
          if (book.createdAt != null) {
            allCreations.add({
              'type': 'book',
              'id': book.id,
              'title': book.name ?? '未命名书籍',
              'subtitle': book.brief ?? '暂无简介',
              'time': _formatTime(book.createdAt!),
              'created_at': DateTime.parse(book.createdAt!),
              'data': book,
            });
          }
        }

        // 添加最近的卡片（从每本书中获取最新的卡片）
        for (final book in books.take(3)) {
          if (book.id != null) {
            final bookCards = await _cardDataService!.getBookCards(
              book.id!,
              orderBy: 'created_at DESC',
              limit: 2,
            );

            for (final card in bookCards) {
              if (card.createdAt != null) {
                allCreations.add({
                  'type': 'card',
                  'id': card.id,
                  'title': card.title ?? '未命名卡片',
                  'subtitle': card.question ?? '暂无问题',
                  'time': _formatTime(card.createdAt!.toIso8601String()),
                  'created_at': card.createdAt!,
                  'data': card,
                });
              }
            }
          }
        }

        // 按创建时间排序，取最近的3条
        allCreations.sort((a, b) => b['created_at'].compareTo(a['created_at']));
        recentCreations = allCreations.take(3).toList();

        Console.log('✅ 本地最近创作加载完成，数量: ${recentCreations.length}');
        for (var item in recentCreations) {
          Console.log('   - ${item['title']} (${item['time']})');
        }
      } else {
        // 回退到网络API
        Console.log('本地数据服务不可用，使用网络API');
        await _loadRecentCreationsFromAPI();
      }
    } catch (e) {
      Console.log('本地最近创作加载失败，回退到网络API: $e');
      await _loadRecentCreationsFromAPI();
    }
  }

  /// 从API加载最近创作数据（回退方案）
  Future<void> _loadRecentCreationsFromAPI() async {
    final userId = AuthController.to.usr.user?.id;
    if (userId == null) {
      Console.log('❌ 用户未登录，无法加载卡片');
      return;
    }

    Map<String, dynamic> cardFilters = {"user_id": userId};

    await asyncRequest(
      () => OxHttp.to.get(HttpUrl.cards, queryParameters: {
        "skip": 0,
        "limit": 3,
        "order": "-created_at",
        "filters": jsonEncode(cardFilters),
      }),
      onSuccess: (data) {
        Console.log('🃏 从API加载最近卡片数据成功，数量: ${data.length}');

        for (var item in data) {
          try {
            String timeStr = '未知时间';
            if (item['created_at'] != null) {
              try {
                timeStr = _formatTime(item['created_at']);
              } catch (e) {
                Console.log('⚠️ 时间格式化失败: $e');
                timeStr = '时间格式错误';
              }
            }

            recentCreations.add({
              'type': 'card',
              'id': item['id'],
              'title': item['title'] ?? '未命名卡片',
              'subtitle': item['question'] ?? '暂无问题',
              'time': timeStr,
              'data': item,
            });
          } catch (e) {
            Console.log('❌ 处理卡片失败: ${item['title']} - 错误: $e');
          }
        }

        Console.log('✅ API最近创作卡片加载完成，数量: ${recentCreations.length}');
      },
      onFailure: (error) {
        Console.log('❌ 加载卡片数据失败: ${error?.msg ?? "未知错误"}');
      },
    );
  }

  /// 格式化时间显示
  String _formatTime(String timeStr) {
    final time = DateTime.parse(timeStr);
    final now = DateTime.now();
    final diff = now.difference(time);

    if (diff.inDays > 0) {
      return '${diff.inDays}天前';
    } else if (diff.inHours > 0) {
      return '${diff.inHours}小时前';
    } else if (diff.inMinutes > 0) {
      return '${diff.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }

  /// 导航方法
  Future<void> toBookCreationPage() async {
    await Get.toNamed(AppRoutes.creationBookList);
  }

  Future<void> toCardCreationPage() async {
    await Get.toNamed(AppRoutes.creationCardList);
  }

  /// 书籍管理方法
  Future<void> loadBookList() async {
    try {
      // 优先使用本地数据服务
      if (_bookDataService != null) {
        if (bookSearchKeyword.isEmpty) {
          bookList = await _bookDataService!.getUserBooks(
            orderBy: 'created_at DESC',
          );
        } else {
          bookList = await _bookDataService!.searchBooks(bookSearchKeyword);
        }
      } else {
        // 回退到网络服务
        bookList = await _bookService.getBookList(
          search: bookSearchKeyword.isEmpty ? null : bookSearchKeyword,
        );
      }
      _applyFiltersAndSort();
      update();
    } catch (e) {
      final friendlyMessage = ErrorHandler.getUserFriendlyMessage(e);
      ShowToast.fail(friendlyMessage);
    }
  }

  void onBookSearchChanged(String keyword) {
    bookSearchKeyword = keyword;
    loadBookList();
  }

  /// 应用筛选和排序
  void _applyFiltersAndSort() {
    List<BookModel> filtered = List.from(bookList);

    // 应用隐私筛选
    if (selectedPrivacyFilter != 'all') {
      filtered = filtered.where((book) {
        return book.privacy == selectedPrivacyFilter;
      }).toList();
    }

    // 应用时间筛选
    if (selectedTimeFilter != 'all') {
      final now = DateTime.now();
      filtered = filtered.where((book) {
        if (book.createdAt == null) return false;
        final createdAt = DateTime.parse(book.createdAt!);

        switch (selectedTimeFilter) {
          case 'today':
            final today = DateTime(now.year, now.month, now.day);
            return createdAt.isAfter(today);
          case 'week':
            final weekStart = now.subtract(Duration(days: now.weekday - 1));
            final weekStartDay =
                DateTime(weekStart.year, weekStart.month, weekStart.day);
            return createdAt.isAfter(weekStartDay);
          case 'month':
            final monthStart = DateTime(now.year, now.month, 1);
            return createdAt.isAfter(monthStart);
          default:
            return true;
        }
      }).toList();
    }

    // 应用排序
    filtered.sort((a, b) {
      switch (selectedSortOption) {
        case 'created_desc':
          return _compareDateTime(b.createdAt, a.createdAt);
        case 'created_asc':
          return _compareDateTime(a.createdAt, b.createdAt);
        case 'updated_desc':
          return _compareDateTime(b.updatedAt, a.updatedAt);
        case 'updated_asc':
          return _compareDateTime(a.updatedAt, b.updatedAt);
        case 'name_asc':
          return (a.name ?? '').compareTo(b.name ?? '');
        case 'name_desc':
          return (b.name ?? '').compareTo(a.name ?? '');
        default:
          return _compareDateTime(b.createdAt, a.createdAt);
      }
    });

    filteredBookList = filtered;
    _updateFilterActiveStatus();
  }

  /// 比较日期时间字符串
  int _compareDateTime(String? a, String? b) {
    if (a == null && b == null) return 0;
    if (a == null) return 1;
    if (b == null) return -1;

    try {
      final dateA = DateTime.parse(a);
      final dateB = DateTime.parse(b);
      return dateA.compareTo(dateB);
    } catch (e) {
      return 0;
    }
  }

  /// 更新筛选激活状态
  void _updateFilterActiveStatus() {
    isFilterActive = selectedPrivacyFilter != 'all' ||
        selectedTimeFilter != 'all' ||
        selectedSortOption != 'created_desc' ||
        bookSearchKeyword.isNotEmpty;
  }

  /// 筛选和排序控制方法
  void setPrivacyFilter(String filter) {
    selectedPrivacyFilter = filter;
    _applyFiltersAndSort();
    update();
  }

  void setTimeFilter(String filter) {
    selectedTimeFilter = filter;
    _applyFiltersAndSort();
    update();
  }

  void setSortOption(String sortOption) {
    selectedSortOption = sortOption;
    _applyFiltersAndSort();
    update();
  }

  void toggleViewMode() {
    isGridView = !isGridView;
    update();
  }

  void clearAllFilters() {
    selectedPrivacyFilter = 'all';
    selectedTimeFilter = 'all';
    selectedSortOption = 'created_desc';
    bookSearchKeyword = '';
    _applyFiltersAndSort();
    update();
  }

  /// 获取筛选选项的显示文本
  String getPrivacyFilterText(String filter) {
    switch (filter) {
      case 'public':
        return '公开';
      case 'private':
        return '私有';
      default:
        return '全部';
    }
  }

  String getTimeFilterText(String filter) {
    switch (filter) {
      case 'today':
        return '今天';
      case 'week':
        return '本周';
      case 'month':
        return '本月';
      default:
        return '全部时间';
    }
  }

  String getSortOptionText(String sortOption) {
    switch (sortOption) {
      case 'created_desc':
        return '创建时间↓';
      case 'created_asc':
        return '创建时间↑';
      case 'updated_desc':
        return '更新时间↓';
      case 'updated_asc':
        return '更新时间↑';
      case 'name_asc':
        return '名称A-Z';
      case 'name_desc':
        return '名称Z-A';
      default:
        return '创建时间↓';
    }
  }

  /// 加载更多书籍
  Future<List<BookModel>> loadMoreBooks() async {
    // 优先使用本地数据服务
    if (_bookDataService != null) {
      if (bookSearchKeyword.isEmpty) {
        return await _bookDataService!.getUserBooks(
          orderBy: 'created_at DESC',
          offset: bookList.length,
          limit: 20,
        );
      } else {
        return await _bookDataService!.searchBooks(
          bookSearchKeyword,
          offset: bookList.length,
          limit: 20,
        );
      }
    } else {
      // 回退到网络服务
      return await _bookService.getBookList(
        skip: bookList.length,
        search: bookSearchKeyword.isEmpty ? null : bookSearchKeyword,
      );
    }
  }

  Future<void> createBook() async {
    final result = await Get.toNamed(AppRoutes.creationBookEdit);
    if (result != null) {
      await loadBookList();
      await loadCreationStats();
    }
  }

  Future<void> editBook(BookModel book) async {
    final result =
        await Get.toNamed(AppRoutes.creationBookEdit, arguments: book);
    if (result != null) {
      await loadBookList();
      await loadCreationStats();
    }
  }

  Future<void> deleteBook(BookModel book) async {
    // 使用UXManager显示确认对话框
    bool confirmed = false;
    if (_uxManager != null) {
      confirmed = await _uxManager!.showConfirmDialog(
        title: '确认删除',
        content: '确定要删除书籍"${book.name}"吗？此操作不可撤销。',
        confirmText: '删除',
        isDestructive: true,
      );
    } else {
      confirmed = await _showDeleteBookDialog(book);
    }

    if (!confirmed) return;

    try {
      // 优先使用本地数据服务
      if (_bookDataService != null) {
        final success = await _bookDataService!.deleteBook(book.id!);
        if (success) {
          bookList.removeWhere((b) => b.id == book.id);
          if (_uxManager != null) {
            _uxManager!.showSuccess('删除成功');
          } else {
            ShowToast.success('删除成功');
          }
          await loadCreationStats();
          update();
        } else {
          throw Exception('删除失败');
        }
      } else {
        // 回退到网络服务
        await _bookService.deleteBook(book.id!);
        bookList.removeWhere((b) => b.id == book.id);
        if (_uxManager != null) {
          _uxManager!.showSuccess('删除成功');
        } else {
          ShowToast.success('删除成功');
        }
        await loadCreationStats();
        update();
      }
    } catch (e) {
      if (_uxManager != null) {
        _uxManager!.showError(e, context: 'delete_book');
      } else {
        final friendlyMessage = ErrorHandler.getUserFriendlyMessage(e);
        ShowToast.fail(friendlyMessage);
      }
    }
  }

  Future<void> viewBookDetail(BookModel book) async {
    await Get.toNamed(AppRoutes.creationBookDetail, arguments: book);
  }

  Future<void> manageBookCards(BookModel book) async {
    await Get.toNamed(AppRoutes.creationCardList,
        arguments: {'bookId': book.id});
  }

  Future<void> duplicateBook(BookModel book) async {
    try {
      // 优先使用本地数据服务
      if (_bookDataService != null) {
        final duplicatedBook = await _bookDataService!.createBook(
          name: '${book.name} - 副本',
          brief: book.brief,
          cover: book.cover,
          privacy: book.privacy,
        );

        if (duplicatedBook != null) {
          ShowToast.success('复制成功');
          await loadBookList();
          await loadCreationStats();
        } else {
          throw Exception('复制失败');
        }
      } else {
        // 回退到网络服务
        final bookData = {
          'name': '${book.name} - 副本',
          'brief': book.brief,
          'privacy': book.privacy,
        };
        await _bookService.createBook(bookData, book.cover);
        ShowToast.success('复制成功');
        await loadBookList();
        await loadCreationStats();
      }
    } catch (e) {
      final friendlyMessage = ErrorHandler.getUserFriendlyMessage(e);
      ShowToast.fail(friendlyMessage);
    }
  }

  Future<bool> _showDeleteBookDialog(BookModel book) async {
    return await Get.dialog<bool>(
          AlertDialog(
            title: const Text('确认删除'),
            content: Text('确定要删除书籍"${book.name}"吗？此操作不可撤销。'),
            actions: [
              TextButton(
                onPressed: () => Get.back(result: false),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () => Get.back(result: true),
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(Get.context!).colorScheme.error,
                ),
                child: const Text('删除'),
              ),
            ],
          ),
        ) ??
        false;
  }

  /// 卡片管理方法
  Future<void> loadCardList({int? bookId, String? cardType}) async {
    try {
      cardList = await _cardService.getCardList(
        search: cardSearchKeyword.isEmpty ? null : cardSearchKeyword,
        bookId: bookId,
        cardType: cardType,
      );
      update();
    } catch (e) {
      final friendlyMessage = ErrorHandler.getUserFriendlyMessage(e);
      ShowToast.fail(friendlyMessage);
    }
  }

  void onCardSearchChanged(String keyword) {
    cardSearchKeyword = keyword;
    loadCardList();
  }

  /// 加载更多卡片
  Future<List<CardModel>> loadMoreCards({int? bookId}) async {
    return await _cardService.getCardList(
      skip: cardList.length,
      search: cardSearchKeyword.isEmpty ? null : cardSearchKeyword,
      bookId: bookId,
    );
  }

  Future<void> createCard({int? bookId}) async {
    final result = await Get.toNamed(AppRoutes.creationCardEdit,
        arguments: {'bookId': bookId});
    if (result != null) {
      await loadCardList(bookId: bookId);
      await loadCreationStats();
    }
  }

  Future<void> editCard(CardModel card) async {
    final result = await Get.toNamed(AppRoutes.creationCardEdit,
        arguments: {'card': card});
    if (result != null) {
      await loadCardList();
      await loadCreationStats();
    }
  }

  Future<void> deleteCard(CardModel card) async {
    final confirmed = await _showDeleteCardDialog(card);
    if (!confirmed) return;

    try {
      await _cardService.deleteCard(card.id!);
      cardList.removeWhere((c) => c.id == card.id);
      ShowToast.success('删除成功');
      await loadCreationStats();
      update();
    } catch (e) {
      final friendlyMessage = ErrorHandler.getUserFriendlyMessage(e);
      ShowToast.fail(friendlyMessage);
    }
  }

  Future<void> viewCardDetail(CardModel card) async {
    await Get.toNamed('/creation/cards/detail', arguments: card);
  }

  Future<void> duplicateCard(CardModel card) async {
    try {
      final cardData = CardModelCreate(
        bookId: null, // CardModel没有bookId字段，复制时不关联书籍
        type: card.type,
        typeVersion: card.typeVersion,
        title: '${card.title} - 副本',
        question: card.question,
        answer: card.answer,
        extra: card.extra as Map<String, dynamic>?,
        cardAssets: card.cardAssets ?? [],
      );

      await _cardService.createCard(cardData);
      ShowToast.success('复制成功');
      await loadCardList();
      await loadCreationStats();
    } catch (e) {
      final friendlyMessage = ErrorHandler.getUserFriendlyMessage(e);
      ShowToast.fail(friendlyMessage);
    }
  }

  Future<void> removeCardFromBook(CardModel card) async {
    try {
      await _cardService.removeCardFromBook(card.id!);
      ShowToast.success('已从书籍中移除');
      await loadCardList();
    } catch (e) {
      final friendlyMessage = ErrorHandler.getUserFriendlyMessage(e);
      ShowToast.fail(friendlyMessage);
    }
  }

  Future<bool> _showDeleteCardDialog(CardModel card) async {
    return await Get.dialog<bool>(
          AlertDialog(
            title: const Text('确认删除'),
            content: Text('确定要删除卡片"${card.title}"吗？此操作不可撤销。'),
            actions: [
              TextButton(
                onPressed: () => Get.back(result: false),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () => Get.back(result: true),
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(Get.context!).colorScheme.error,
                ),
                child: const Text('删除'),
              ),
            ],
          ),
        ) ??
        false;
  }

  Future<void> toAICardCreationPage() async {
    // TODO: 实现AI创作卡片页面
    ShowToast.success("AI创作卡片功能即将上线");
  }

  Future<void> toCreationListPage() async {
    // TODO: 实现创作列表页面
    ShowToast.success("创作列表页面即将上线");
  }

  Future<void> toCreationSettings() async {
    // TODO: 实现创作设置页面
    ShowToast.success("创作设置页面即将上线");
  }

  /// 打开最近创作项目 - 只处理卡片
  void openRecentCreation(Map<String, dynamic> item) {
    if (item['type'] == 'card') {
      final cardModel = CardModel.fromJson(item['data']);
      toCardDetailPage(cardModel: cardModel);
    }
  }

  /// 跳转到卡片详情页面
  Future<void> toCardDetailPage({required CardModel cardModel}) async {
    await Get.toNamed(AppRoutes.review, arguments: {
      "type": StudyType.info,
      "cardModel": cardModel,
    });
  }

  // 调试方法
  void debugPrintStatus() {
    print('🔍 调试信息:');
    print('   - 总书籍数: $totalBooks');
    print('   - 总卡片数: $totalCards');
    print('   - 最近创作数量: ${recentCreations.length}');
    print('   - 最近创作内容: $recentCreations');
    print('   - 用户ID: ${AuthController.to.usr.user?.id}');
  }

  // 手动刷新数据
  Future<void> manualRefresh() async {
    print('🔄 手动刷新数据...');
    await loadCreationStats();
    ShowToast.success('数据刷新完成');
  }

  // 保留原有的一些方法用于兼容性

  /// 刷新数据
  Future<void> onRefresh() async {
    await loadCreationStats();
  }

  Future toBookEditPage({BookModel? bookModel}) async {
    await Get.toNamed(AppRoutes.bookEditor, arguments: bookModel)
        ?.then((e) async {
      await onRefresh();
    }).then((e) {
      update();
    });
  }

  Future toBookInfoPage({BookModel? bookModel}) async {
    await Get.toNamed(AppRoutes.bookInfo, arguments: bookModel)
        ?.then((e) async {
      await onRefresh();
    }).then((e) {
      update();
    });
  }

  onDeleteBook(BookModel bookModel) {
    Sdialog.show(
      cancel: const Text("取消"),
      onCancel: () => Get.back(),
      confirm: const Text("确认"),
      onConfirm: () async {
        await asyncRequest(
          () => OxHttp.to.delete("${HttpUrl.books}/${bookModel.id}"),
          onSuccess: (data) {
            // 删除成功后刷新统计数据
            loadCreationStats();
            ShowToast.success("删除成功");
          },
          onFailure: (error) {
            final friendlyMessage = ErrorHandler.getUserFriendlyMessage(error);
            ShowToast.fail(friendlyMessage);
          },
        );
        Get.back();
      },
      builder: (BuildContext context) {
        return const Text("确定删除吗? 该操作不可逆");
      },
    );
  }

  selectEditingBook(int id) async {
    await Get.toNamed(AppRoutes.cardEditList, arguments: id);
  }

  Future<void> updateConfigModel() async {
    await asyncRequest(
      () => OxHttp.to
          .put(HttpUrl.config, data: AuthController.to.usr.config?.toJson()),
      onSuccess: (data) {
        AuthController.to.usr.config = ConfigModel.fromJson(data);
        AuthController.to.updateUsr();
      },
      onFailure: (error) {
        ShowToast.fail(error?.msg ?? "未知错误");
      },
    );
  }

  void toCardEditList() async {
    Console.log('to卡片本列表');
    await Get.toNamed(AppRoutes.cardEditList);
    await onRefresh();
    update();
  }

  /// 同步本地数据到云端 - 使用SyncManager
  Future<void> syncToCloud() async {
    if (isSyncing) {
      ShowToast.text('同步正在进行中...');
      return;
    }

    try {
      ShowToast.loading(val: '正在同步数据...');

      // 使用SyncManager进行同步
      bool success = false;
      if (_syncManager != null) {
        success = await _syncManager!.performFullSync();
      } else {
        // 回退到原有的同步逻辑
        await _performLegacySync();
        success = true;
      }

      ShowToast.dismiss();

      if (success) {
        ShowToast.success('数据同步成功');
        // 同步成功后重新加载数据
        await loadCreationStats();
      } else {
        ShowToast.fail('数据同步失败');
      }
    } catch (e) {
      ShowToast.dismiss();
      final friendlyMessage = ErrorHandler.getUserFriendlyMessage(e);
      ShowToast.fail(friendlyMessage);
      Console.log('同步失败: $e');
    }
  }

  /// 执行传统同步逻辑（回退方案）
  Future<void> _performLegacySync() async {
    setSyncState(const SyncState(status: SyncStatus.syncing, progress: 0.0));

    // 1. 上传本地未同步的数据
    await _syncLocalToRemote();
    setSyncState(_syncState.copyWith(progress: 0.5));

    // 2. 下载远程最新数据
    await _syncRemoteToLocal();
    setSyncState(_syncState.copyWith(progress: 1.0));

    setSyncState(SyncState(
      status: SyncStatus.success,
      progress: 1.0,
      lastSyncTime: DateTime.now(),
    ));
  }

  /// 上传本地数据到远程
  Future<void> _syncLocalToRemote() async {
    Console.log('开始上传本地数据到远程...');

    // 上传书籍数据
    if (_bookDataService != null) {
      final success = await _bookDataService!.syncLocalBooksToApi();
      Console.log('书籍数据上传结果: $success');
    }

    // 上传卡片数据
    if (_cardDataService != null) {
      final success = await _cardDataService!.manualSync();
      Console.log('卡片数据上传结果: $success');
    }
  }

  /// 从远程下载数据到本地
  Future<void> _syncRemoteToLocal() async {
    Console.log('开始从远程下载数据到本地...');

    // 检查同步服务
    if (!Get.isRegistered<ApiSyncService>()) {
      Get.put<ApiSyncService>(ApiSyncService());
      await Get.find<ApiSyncService>().init();
    }

    final apiSyncService = Get.find<ApiSyncService>();

    // 同步书籍数据
    await apiSyncService.syncTable('books');

    // 同步卡片数据
    await apiSyncService.syncTable('cards');
  }

  /// 仅同步书籍数据 - 使用SyncManager
  Future<void> syncBooksOnly() async {
    if (isSyncing) {
      ShowToast.text('同步正在进行中...');
      return;
    }

    try {
      ShowToast.loading(val: '正在同步书籍数据...');

      bool success = false;
      if (_syncManager != null) {
        success = await _syncManager!.syncBooksOnly();
      } else {
        // 回退到原有的同步逻辑
        await _performLegacyBookSync();
        success = true;
      }

      ShowToast.dismiss();

      if (success) {
        ShowToast.success('书籍数据同步成功');
        // 同步成功后重新加载书籍列表
        await loadBookList();
      } else {
        ShowToast.fail('书籍数据同步失败');
      }
    } catch (e) {
      ShowToast.dismiss();
      final friendlyMessage = ErrorHandler.getUserFriendlyMessage(e);
      ShowToast.fail(friendlyMessage);
      Console.log('书籍同步失败: $e');
    }
  }

  /// 执行传统书籍同步逻辑（回退方案）
  Future<void> _performLegacyBookSync() async {
    _setSyncStatus('syncing', 0.0);

    // 1. 首先上传本地未同步的书籍
    if (Get.isRegistered<BookDataService>()) {
      final bookDataService = BookDataService.to;
      final uploadSuccess = await bookDataService.syncLocalBooksToApi();
      Console.log('本地书籍上传结果: $uploadSuccess');
      _setSyncStatus('syncing', 0.5);
    }

    // 2. 然后从服务器下载最新的书籍数据
    if (!Get.isRegistered<ApiSyncService>()) {
      Get.put<ApiSyncService>(ApiSyncService());
      await Get.find<ApiSyncService>().init();
    }

    final apiSyncService = Get.find<ApiSyncService>();
    final success = await apiSyncService.syncTable('books');

    if (success) {
      _setSyncStatus('success', 1.0);
    } else {
      _setSyncStatus('failed', 0.0, '书籍同步失败');
      throw Exception('书籍同步失败');
    }
  }

  /// 设置同步状态
  void _setSyncStatus(String status, double progress, [String? errorMessage]) {
    SyncStatus syncStatusEnum;
    switch (status) {
      case 'syncing':
        syncStatusEnum = SyncStatus.syncing;
        break;
      case 'success':
        syncStatusEnum = SyncStatus.success;
        break;
      case 'failed':
        syncStatusEnum = SyncStatus.failed;
        break;
      default:
        syncStatusEnum = SyncStatus.idle;
    }

    _syncState = _syncState.copyWith(
      status: syncStatusEnum,
      progress: progress,
      message: errorMessage,
      lastSyncTime: syncStatusEnum == SyncStatus.success
          ? DateTime.now()
          : _syncState.lastSyncTime,
    );
    update();
  }

  /// 重置同步状态
  void resetSyncStatus() {
    _syncState = const SyncState(status: SyncStatus.idle);
    update();
  }

  /// 设置同步状态（新方法，使用SyncState）
  void setSyncState(SyncState newState) {
    _syncState = newState;
    update();
  }
}
