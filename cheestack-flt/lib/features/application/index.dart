library application_page;

import 'package:cheestack_flt/apis/index.dart';
import 'package:cheestack_flt/shared/widgets/index.dart';
import 'package:cheestack_flt/global.dart';
import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/features/creation/index.dart';
import 'package:cheestack_flt/pages/home/<USER>';
import 'package:cheestack_flt/pages/listening/index.dart';
import 'package:cheestack_flt/features/profile/index.dart';
import 'package:cheestack_flt/features/study/index.dart';
import 'package:cheestack_flt/shared/utils/index.dart';
import 'package:cheestack_flt/i18n/index.dart';
import 'package:flutter/material.dart';
// import 'package:flutter_gromore/flutter_gromore.dart';
import 'package:flutter_lifecycle_detector/flutter_lifecycle_detector.dart';
import 'package:get/get.dart';
import 'package:ionicons/ionicons.dart';
import 'package:lifecycle/lifecycle.dart';

part 'controller.dart';
part 'view.dart';
