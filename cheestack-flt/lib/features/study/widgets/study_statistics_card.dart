part of study;

/// 学习统计卡片组件
class StudyStatisticsCard extends StatelessWidget {
  const StudyStatisticsCard({
    Key? key,
    required this.statistics,
    this.onTap,
  }) : super(key: key);

  final StudyStatistics statistics;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge.w),
      child: Container(
        padding: AppTheme.paddingMedium,
        decoration: BoxDecoration(
          color: Theme.of(Get.context!).colorScheme.surface,
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge.w),
          boxShadow: [
            BoxShadow(
              color: Theme.of(Get.context!)
                  .colorScheme
                  .primary
                  .withValues(alpha: 0.1),
              blurRadius: 8.w,
              offset: Offset(0, 2.w),
            ),
          ],
        ),
        child: LayoutBuilder(
          builder: (context, constraints) {
            // 在小屏幕上使用垂直布局，大屏幕上使用水平布局
            final isSmallScreen = constraints.maxWidth < 600.w;

            if (isSmallScreen) {
              return Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatItem(
                          icon: Icons.today_outlined,
                          title: '今日学习',
                          value: '${statistics.todayTotal}',
                          subtitle: '张卡片',
                          color: Theme.of(Get.context!).colorScheme.primary,
                        ),
                      ),
                      Container(
                        width: 1.w,
                        height: 40.h,
                        color: Theme.of(Get.context!)
                            .colorScheme
                            .outline
                            .withValues(alpha: 0.3),
                      ),
                      Expanded(
                        child: _buildStatItem(
                          icon: Icons.local_fire_department_outlined,
                          title: '连续天数',
                          value: '${statistics.consecutiveDays}',
                          subtitle: '天',
                          color: Theme.of(Get.context!).colorScheme.secondary,
                        ),
                      ),
                    ],
                  ),
                  Container(
                    height: 1.h,
                    margin: EdgeInsets.symmetric(vertical: 8.h),
                    color: Theme.of(Get.context!)
                        .colorScheme
                        .outline
                        .withValues(alpha: 0.3),
                  ),
                  _buildStatItem(
                    icon: Icons.trending_up_outlined,
                    title: '总体进度',
                    value: '${(statistics.overallProgress * 100).toInt()}%',
                    subtitle: '完成度',
                    color: Theme.of(Get.context!).colorScheme.tertiary,
                  ),
                ],
              );
            } else {
              return Row(
                children: [
                  Expanded(
                    child: _buildStatItem(
                      icon: Icons.today_outlined,
                      title: '今日学习',
                      value: '${statistics.todayTotal}',
                      subtitle: '张卡片',
                      color: Theme.of(Get.context!).colorScheme.primary,
                    ),
                  ),
                  Container(
                    width: 1.w,
                    height: 40.h,
                    color: Theme.of(Get.context!)
                        .colorScheme
                        .outline
                        .withValues(alpha: 0.3),
                  ),
                  Expanded(
                    child: _buildStatItem(
                      icon: Icons.local_fire_department_outlined,
                      title: '连续天数',
                      value: '${statistics.consecutiveDays}',
                      subtitle: '天',
                      color: Theme.of(Get.context!).colorScheme.secondary,
                    ),
                  ),
                  Container(
                    width: 1.w,
                    height: 40.h,
                    color: Theme.of(Get.context!)
                        .colorScheme
                        .outline
                        .withValues(alpha: 0.3),
                  ),
                  Expanded(
                    child: _buildStatItem(
                      icon: Icons.trending_up_outlined,
                      title: '总体进度',
                      value: '${(statistics.overallProgress * 100).toInt()}%',
                      subtitle: '完成度',
                      color: Theme.of(Get.context!).colorScheme.tertiary,
                    ),
                  ),
                ],
              );
            }
          },
        ),
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String title,
    required String value,
    required String subtitle,
    required Color color,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 24.w,
          color: color,
        ),
        SizedBox(height: 4.h),
        Text(
          title,
          style: TextStyle(
            fontSize: AppTheme.fontSmall,
            color: Theme.of(Get.context!).colorScheme.onSurfaceVariant,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 2.h),
        Text(
          value,
          style: TextStyle(
            
            color: color,
            
          ),
          textAlign: TextAlign.center,
        ),
        Text(
          subtitle,
          style: TextStyle(
            fontSize: 10.sp,
            color: Theme.of(Get.context!).colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}

/// 主要学习按钮组件
class MainStudyButton extends StatelessWidget {
  const MainStudyButton({
    Key? key,
    required this.statistics,
    required this.onPressed,
    this.isLoading = false,
  }) : super(key: key);

  final StudyStatistics statistics;
  final VoidCallback onPressed;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    final hasTask = statistics.hasStudyTasks;
    final recommendation = statistics.getStudyRecommendation();

    return Container(
      width: double.infinity,
      padding: AppTheme.paddingMedium,
      decoration: BoxDecoration(
        gradient: hasTask
            ? LinearGradient(
                colors: [
                  Theme.of(Get.context!).colorScheme.primary,
                  Theme.of(Get.context!)
                      .colorScheme
                      .primary
                      .withValues(alpha: 0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              )
            : null,
        color: hasTask
            ? null
            : Theme.of(Get.context!).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge.w),
        boxShadow: hasTask
            ? [
                BoxShadow(
                  color: Theme.of(Get.context!)
                      .colorScheme
                      .primary
                      .withValues(alpha: 0.3),
                  blurRadius: 12.w,
                  offset: Offset(0, 4.w),
                ),
              ]
            : null,
      ),
      child: InkWell(
        onTap: hasTask ? onPressed : null,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge.w),
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 20.h),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (isLoading)
                SizedBox(
                  width: 24.w,
                  height: 24.w,
                  child: CircularProgressIndicator(
                    strokeWidth: 2.w,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      hasTask
                          ? Theme.of(Get.context!).colorScheme.onPrimary
                          : Theme.of(Get.context!).colorScheme.primary,
                    ),
                  ),
                )
              else
                Icon(
                  hasTask ? Icons.play_circle_filled : Icons.check_circle,
                  size: 48.w,
                  color: hasTask
                      ? Theme.of(Get.context!).colorScheme.onPrimary
                      : Theme.of(Get.context!).colorScheme.primary,
                ),
              SizedBox(height: 12.h),
              Text(
                hasTask ? '开始学习' : '今日完成',
                style: TextStyle(
                  fontSize: AppTheme.fontLarge,
                  
                  color: hasTask
                      ? Theme.of(Get.context!).colorScheme.onPrimary
                      : Theme.of(Get.context!).colorScheme.primary,
                ),
              ),
              SizedBox(height: 4.h),
              Text(
                recommendation,
                style: TextStyle(
                  
                  color: hasTask
                      ? Theme.of(Get.context!)
                          .colorScheme
                          .onPrimary
                          .withValues(alpha: 0.9)
                      : Theme.of(Get.context!).colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 快速操作按钮组件
class QuickActionButton extends StatelessWidget {
  const QuickActionButton({
    Key? key,
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onPressed,
    this.color,
    this.isExpanded = true,
  }) : super(key: key);

  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onPressed;
  final Color? color;
  final bool isExpanded;

  @override
  Widget build(BuildContext context) {
    final buttonColor = color ?? Theme.of(Get.context!).colorScheme.primary;

    final buttonWidget = InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium.w),
      child: Container(
        width: isExpanded ? null : double.infinity,
        padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 12.w),
        decoration: BoxDecoration(
          color: Theme.of(Get.context!).colorScheme.surface,
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium.w),
          border: Border.all(
            color: Theme.of(Get.context!)
                .colorScheme
                .outline
                .withValues(alpha: 0.2),
            width: 1.w,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 32.w,
              color: buttonColor,
            ),
            SizedBox(height: 8.h),
            Text(
              title,
              style: TextStyle(
                
                
                color: Theme.of(Get.context!).colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 2.h),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: AppTheme.fontSmall,
                color: Theme.of(Get.context!).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );

    return isExpanded ? Expanded(child: buttonWidget) : buttonWidget;
  }
}
