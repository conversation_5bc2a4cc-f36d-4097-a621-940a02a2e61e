part of study;

class StudyPage extends StatefulWidget {
  const StudyPage({Key? key}) : super(key: key);

  @override
  State<StudyPage> createState() => _StudyPageState();
}

class _StudyPageState extends State<StudyPage>
    with AutomaticKeepAliveClientMixin, RouteAware {
  StudyController get controller => Get.find<StudyController>();

  @override
  bool get wantKeepAlive => true;

  @override
  void didChangeDependencies() {
    // 实际使用如下:
    AppRoutes.observer.subscribe(this, ModalRoute.of(context) as Route);
    super.didChangeDependencies();
  }

  @override
  void didPopNext() {
    controller.fetchStudyStatistics();
    super.didPopNext();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return GetBuilder<StudyController>(
        init: StudyController(),
        builder: (controller) {
          return Scaffold(
            appBar: _buildAppBar(controller),
            body: SmartRefresher(
              controller: controller.refreshController,
              onRefresh: controller.onRefresh,
              child: _buildView(),
            ),
          );
        });
  }

  _buildAppBar(StudyController controller) {
    return OxAppBar(
      title: Text(LocaleKeys.studyTitle.tr),
      actions: [
        IconButton(
          icon: const Icon(Icons.library_books_outlined),
          onPressed: controller.toBookManagePage,
          tooltip: '管理书本',
        ),
      ],
    );
  }

  _buildView() {
    if (controller.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (controller.bookScheduleList.isEmpty) {
      return _buildEmptyState();
    }

    return Padding(
      padding: AppTheme.paddingMedium,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 学习统计卡片
          StudyStatisticsCard(
            statistics: controller.statistics,
            onTap: null, // 移除点击事件，统计信息已在主页面显示
          ),

          // 今日任务进度区域
          _buildTodayProgressSection(controller),

          // 紧凑的学习按钮
          _buildCompactStudyButton(controller),
        ],
      ),
    );
  }

  /// 构建空状态视图
  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: AppTheme.paddingLarge,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.school_outlined,
              size: 64.w,
              color: Theme.of(Get.context!)
                  .colorScheme
                  .primary
                  .withValues(alpha: 0.6),
            ),
            Text(
              '还没有学习计划',
              style: TextStyle(
                fontSize: AppTheme.fontLarge,
                
                color: Theme.of(Get.context!).colorScheme.onSurface,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              '添加一些书本开始学习吧！',
              style: TextStyle(
                
                color: Theme.of(Get.context!).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            ElevatedButton.icon(
              onPressed: controller.toBookManagePage,
              icon: const Icon(Icons.add),
              label: const Text('添加书本'),
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建今日任务进度区域
  Widget _buildTodayProgressSection(StudyController controller) {
    final statistics = controller.statistics;
    final totalTasks = statistics.studyLeft + statistics.reviewLeft;
    final completedTasks = statistics.todayTotal;
    final dailyTarget = controller.settings.dailyNewCards +
        controller.settings.dailyReviewCards;
    final progress = dailyTarget > 0 ? completedTasks / dailyTarget : 0.0;

    return Card(
      child: Padding(
        padding: AppTheme.paddingMedium,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '今日任务',
                  style: TextStyle(
                    fontSize: AppTheme.fontLarge,
                    
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                Text(
                  '$completedTasks / $dailyTarget',
                  style: TextStyle(
                    
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            LinearProgressIndicator(
              value: progress.clamp(0.0, 1.0),
              backgroundColor:
                  Theme.of(context).colorScheme.surfaceContainerHighest,
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(height: 8.h),
            if (totalTasks > 0)
              Text(
                statistics.getStudyRecommendation(),
                style: TextStyle(
                  fontSize: AppTheme.fontSmall,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              )
            else
              Text(
                '🎉 今日任务已完成！',
                style: TextStyle(
                  fontSize: AppTheme.fontSmall,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// 构建紧凑的学习按钮
  Widget _buildCompactStudyButton(StudyController controller) {
    final hasTask = controller.hasStudyTasks;

    return SizedBox(
      width: double.infinity,
      height: 48.h,
      child: ElevatedButton.icon(
        onPressed: hasTask ? controller.startSmartStudy : null,
        icon: controller.isLoading
            ? SizedBox(
                width: 16.w,
                height: 16.w,
                child: const CircularProgressIndicator(strokeWidth: 2),
              )
            : Icon(hasTask ? Icons.play_arrow : Icons.check),
        label: Text(hasTask ? '开始学习' : '今日完成'),
        style: ElevatedButton.styleFrom(
          backgroundColor: hasTask
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.surfaceContainerHighest,
          foregroundColor: hasTask
              ? Theme.of(context).colorScheme.onPrimary
              : Theme.of(context).colorScheme.onSurfaceVariant,
        ),
      ),
    );
  }


}
