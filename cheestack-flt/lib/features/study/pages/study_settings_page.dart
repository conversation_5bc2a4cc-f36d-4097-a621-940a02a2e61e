import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:cheestack_flt/models/index.dart';

/// 学习设置页面
class StudySettingsPage extends StatefulWidget {
  const StudySettingsPage({Key? key}) : super(key: key);

  @override
  State<StudySettingsPage> createState() => _StudySettingsPageState();
}

class _StudySettingsPageState extends State<StudySettingsPage> {
  late StudySettings settings;
  bool isLoading = true;
  bool isSaving = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      // TODO: 从API加载设置
      settings = StudySettings();
      setState(() {
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      ShowToast.fail('加载设置失败');
    }
  }

  Future<void> _saveSettings() async {
    setState(() {
      isSaving = true;
    });

    try {
      // TODO: 保存设置到API
      await Future.delayed(const Duration(seconds: 1)); // 模拟API调用
      ShowToast.success('设置保存成功');
      Get.back();
    } catch (e) {
      ShowToast.fail('保存设置失败');
    } finally {
      setState(() {
        isSaving = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('学习设置'),
        actions: [
          if (!isLoading)
            TextButton(
              onPressed: isSaving ? null : _saveSettings,
              child: isSaving
                  ? SizedBox(
                      width: 16.w,
                      height: 16.w,
                      child: const CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('保存'),
            ),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: AppTheme.paddingMedium,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSectionTitle('学习策略'),
                  _buildStrategySection(),
                  _buildSectionTitle('每日目标'),
                  _buildDailyTargetSection(),
                  _buildSectionTitle('音频设置'),
                  _buildAudioSection(),
                  _buildSectionTitle('书本管理'),
                  _buildBookManagementSection(),
                ],
              ),
            ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: Text(
        title,
        style: TextStyle(
          fontSize: AppTheme.fontLarge,
          
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildStrategySection() {
    return Card(
      child: Padding(
        padding: AppTheme.paddingMedium,
        child: Column(
          children: [
            RadioListTile<bool>(
              title: const Text('优先复习'),
              subtitle: const Text('先完成到期复习，再学习新内容'),
              value: true,
              groupValue: settings.prioritizeReview,
              onChanged: (value) {
                setState(() {
                  settings = settings.copyWith(prioritizeReview: value);
                });
              },
            ),
            RadioListTile<bool>(
              title: const Text('优先学新'),
              subtitle: const Text('先学习新内容，再进行复习'),
              value: false,
              groupValue: settings.prioritizeReview,
              onChanged: (value) {
                setState(() {
                  settings = settings.copyWith(prioritizeReview: value);
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDailyTargetSection() {
    return Card(
      child: Padding(
        padding: AppTheme.paddingMedium,
        child: Column(
          children: [
            ListTile(
              title: const Text('每日新学数量'),
              subtitle: Text('当前: ${settings.dailyNewCards} 张'),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    onPressed: settings.dailyNewCards > 1
                        ? () {
                            setState(() {
                              settings = settings.copyWith(
                                dailyNewCards: settings.dailyNewCards - 1,
                              );
                            });
                          }
                        : null,
                    icon: const Icon(Icons.remove),
                  ),
                  IconButton(
                    onPressed: settings.dailyNewCards < 50
                        ? () {
                            setState(() {
                              settings = settings.copyWith(
                                dailyNewCards: settings.dailyNewCards + 1,
                              );
                            });
                          }
                        : null,
                    icon: const Icon(Icons.add),
                  ),
                ],
              ),
            ),
            const Divider(),
            ListTile(
              title: const Text('每日复习数量'),
              subtitle: Text('当前: ${settings.dailyReviewCards} 张'),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    onPressed: settings.dailyReviewCards > 1
                        ? () {
                            setState(() {
                              settings = settings.copyWith(
                                dailyReviewCards: settings.dailyReviewCards - 1,
                              );
                            });
                          }
                        : null,
                    icon: const Icon(Icons.remove),
                  ),
                  IconButton(
                    onPressed: settings.dailyReviewCards < 100
                        ? () {
                            setState(() {
                              settings = settings.copyWith(
                                dailyReviewCards: settings.dailyReviewCards + 1,
                              );
                            });
                          }
                        : null,
                    icon: const Icon(Icons.add),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAudioSection() {
    return Card(
      child: Padding(
        padding: AppTheme.paddingMedium,
        child: Column(
          children: [
            SwitchListTile(
              title: const Text('自动播放音频'),
              subtitle: const Text('学习时自动播放卡片音频'),
              value: settings.autoPlayAudio,
              onChanged: (value) {
                setState(() {
                  settings = settings.copyWith(autoPlayAudio: value);
                });
              },
            ),
            SwitchListTile(
              title: const Text('自动播放AI音频'),
              subtitle: const Text('学习时自动播放AI生成的音频'),
              value: settings.autoPlayAiAudio,
              onChanged: (value) {
                setState(() {
                  settings = settings.copyWith(autoPlayAiAudio: value);
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBookManagementSection() {
    return Card(
      child: Padding(
        padding: AppTheme.paddingMedium,
        child: Column(
          children: [
            ListTile(
              leading: const Icon(Icons.library_books),
              title: const Text('管理学习书本'),
              subtitle: const Text('选择、排序和设置书本优先级'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                // TODO: 跳转到书本管理页面
                ShowToast.text('书本管理功能开发中...');
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.refresh),
              title: const Text('重置设置'),
              subtitle: const Text('恢复到默认设置'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                _showResetDialog();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showResetDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('重置设置'),
        content: const Text('确定要恢复到默认设置吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                settings = StudySettings();
              });
              Navigator.of(context).pop();
              ShowToast.success('设置已重置');
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
