import 'dart:async';
import 'package:cheestack_flt/controllers/index.dart';
import 'package:cheestack_flt/shared/utils/index.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';

class SrecorderView extends StatefulWidget {
  const SrecorderView({
    Key? key,
  }) : super(key: key);

  @override
  SrecorderViewState createState() => SrecorderViewState();
}

class SrecorderViewState extends State<SrecorderView> {
  bool isRecording = false;
  bool isScoring = false;

  /// the limit time to speech, according to the audio duration
  Timer timer = Timer(Duration.zero, () {});

  /// try times
  int tryTimes = 0;

  bool isAllowClick = true;

  /// max try times
  int maxTryTimes = 3;

  /// 当前时间
  DateTime recordTime = DateTime.now();

  _stopPlayOrRecord() async {
    await Ssound.to.startRecord();
    await Ssound.to.stopPlay();
    await OxAudioController.to.stop();
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const SizedBox(height: 16),
        _buildWaveOrRateBar(),
        const SizedBox(height: 16),
        Expanded(child: _buildSpeechActionBar()),
      ],
    );
  }

  _buildSpeechActionBar() {
    if (isRecording) {
      return Sbutton(
          size: SbuttonSize.large,
          shape: SbuttonShape.outline,
          backgroundColor: AppTheme.primary,
          child: const Text("停止录音"),
          onPressed: () async {
            if (timer.isActive) timer.cancel();
            await _stopRecording();
          });
    } else {
      return Sbutton(
        size: SbuttonSize.large,
        shape: SbuttonShape.outline,
        backgroundColor: AppTheme.primary,
        onPressed: _startRecording,
        child: const Text("点击录音"),
      );
    }
  }

  Future _startRecording() async {
    setState(() {
      isAllowClick = false;
      isRecording = true;
    });
    recordTime = DateTime.now();
    await _stopPlayOrRecord();
    await Ssound().startRecord();
    timer = Timer(_getTimeOut(), () async {
      await _stopRecording();
    });
  }

  Future _stopRecording() async {
    await Ssound.to.stopRecord();
    if (DateTime.now().difference(recordTime).inMilliseconds < 1000) {
      ShowToast.fail('录音时间太短');
      setState(() {
        isRecording = false;
      });
      return;
    }
    setState(() {
      isRecording = Ssound.to.isRecording;
    });
  }

  _getTimeOut() {
    Duration duration = OxAudioController.to.duration;
    if (duration == Duration.zero) {
      Console.log(duration);
      return const Duration(seconds: 5);
    } else {
      return duration + const Duration(seconds: 2);
    }
  }

  _buildWaveOrRateBar() {
    return SizedBox(
      height: 60,
      child: isRecording ? ChvAudioWave() : Container(),
    );
  }
}

class SrecorderController extends GetxController {
  static SrecorderController get to => Get.find();
}
