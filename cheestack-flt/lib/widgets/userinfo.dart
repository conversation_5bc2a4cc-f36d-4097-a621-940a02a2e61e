part of widgets;

class ChvUserInfo extends StatelessWidget {
  final String? avatar;
  final String? name;
  final String? mobile;
  final String? intro;
  final OxAvatarShape avatarShape;

  const ChvUserInfo({
    Key? key,
    this.avatar,
    this.avatarShape = OxAvatarShape.radius,
    this.name,
    this.mobile,
    this.intro,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Row(
      children: [
        /// user avatar, when tap, show the avatar in a gallery
        _buildUserAvatar(context),
        const SizedBox(width: 16),

        /// 用户信息
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                name ?? '',
                style: TextStyle(
                  fontSize: FontSize.title,
                  color: colorScheme.onSurface,
                ),
              ),
              Text(mobile ?? '' ?? ""),
              // Row组件包裹一个`intro`的Text组件和`箭头图标`
              Row(
                children: [
                  Expanded(
                    child: Text(
                      intro ?? '',
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios_rounded,
                    size: IconSize.body,
                    color: colorScheme.primary,
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  OxAvatar _buildUserAvatar(BuildContext context) {
    return OxAvatar(
      url: avatar ?? "",
      size: OxAvatarSize.medium,
      onTap: () {
        CustomGallery.show(
          context: context,
          items: [
            CustomGalleryItem(
              type: SimageType.network,
              url: avatar ?? '',
            ),
          ],
        );
      },
    );
  }
}
