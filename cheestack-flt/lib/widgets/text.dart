part of widgets;

/// Material 3 文本样式枚举
///
/// 基于 Material 3 设计规范的预定义文本样式
enum OxTextStyle {
  // 显示文本 - 用于最重要的文本，如页面标题
  displayLarge,
  displayMedium,
  displaySmall,

  // 标题文本 - 用于页面主要标题
  headlineLarge,
  headlineMedium,
  headlineSmall,

  // 标题 - 用于章节标题
  titleLarge,
  titleMedium,
  titleSmall,

  // 正文 - 用于主要内容
  bodyLarge,
  bodyMedium,
  bodySmall,

  // 标签 - 用于按钮和标签
  labelLarge,
  labelMedium,
  labelSmall,
}

/// OxTextStyle扩展方法，用于将枚举转换为TextStyle
extension OxTextStyleExtension on OxTextStyle {
  /// 根据上下文获取对应的TextStyle
  TextStyle? getTextStyle(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    switch (this) {
      case OxTextStyle.displayLarge:
        return textTheme.displayLarge;
      case OxTextStyle.displayMedium:
        return textTheme.displayMedium;
      case OxTextStyle.displaySmall:
        return textTheme.displaySmall;
      case OxTextStyle.headlineLarge:
        return textTheme.headlineLarge;
      case OxTextStyle.headlineMedium:
        return textTheme.headlineMedium;
      case OxTextStyle.headlineSmall:
        return textTheme.headlineSmall;
      case OxTextStyle.titleLarge:
        return textTheme.titleLarge;
      case OxTextStyle.titleMedium:
        return textTheme.titleMedium;
      case OxTextStyle.titleSmall:
        return textTheme.titleSmall;
      case OxTextStyle.bodyLarge:
        return textTheme.bodyLarge;
      case OxTextStyle.bodyMedium:
        return textTheme.bodyMedium;
      case OxTextStyle.bodySmall:
        return textTheme.bodySmall;
      case OxTextStyle.labelLarge:
        return textTheme.labelLarge;
      case OxTextStyle.labelMedium:
        return textTheme.labelMedium;
      case OxTextStyle.labelSmall:
        return textTheme.labelSmall;
    }
  }
}
