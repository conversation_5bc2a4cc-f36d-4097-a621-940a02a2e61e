part of widgets;

class CustomTag extends StatelessWidget {
  final Widget text;
  final Color? color;

  const CustomTag({
    Key? key,
    required this.text,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final surfaceColor = color ?? Theme.of(context).colorScheme.onSurface;
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 10.w,
        vertical: 7.w,
      ),
      decoration: ShapeDecoration(
        color: surfaceColor.withValues(red: 0, green: 0, blue: 0, alpha: 0.1),
        shape: const StadiumBorder(),
      ),
      child: DefaultTextStyle.merge(
        textAlign: TextAlign.center,
        style: TextStyle(
          fontSize: 14.w,
          
          color: surfaceColor,
          height: 1.3,
        ),
        child: text,
      ),
    );
  }
}
