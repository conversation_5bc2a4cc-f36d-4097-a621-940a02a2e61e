part of widgets;

/// test
class CustomCard extends StatelessWidget {
  final Widget? child;
  final List<Color>? gradientColors;
  final Color? borderColor;
  final bool showBorder;
  final Color? shadowColor;
  final double? width;
  final double? height;
  final Clip clipBehavior;
  final void Function()? onTap;

  const CustomCard(
      {Key? key,
      this.child,
      this.gradientColors,
      this.borderColor,
      this.showBorder = true,
      this.shadowColor,
      this.width,
      this.height,
      this.onTap,
      this.clipBehavior = Clip.none})
      : super(key: key);

  // 工厂函数, 作用是为了提供一种更有意义的方式去创建一个类的实例
  factory CustomCard.common({
    Key? key,

    /// 标题, 位于卡片顶部
    required Widget title,

    /// 标签, 位于标题右侧
    Widget? tag,

    /// 副标题, 位于标题下方
    Widget? subtitle,

    /// 标签, 位于副标题下方
    Widget? label,

    /// 突出显眼的文字, 位于卡片右下角
    Widget? iconText,

    /// 位于卡片底部, 有横线分割, 可以放置一些按钮或者其他内容
    Widget? bottom,

    /// switch图标, 位于卡片右上角, 可用于收藏等
    Widget? toggleIcon,

    /// 图片, 位于卡片左侧, 用于头像等
    Widget? image,

    /// 点击事件
    void Function()? onTap,
  }) = _CardCommon;

  factory CustomCard.chat({
    Key? key,
    required Widget nickname,
    String? avatar,
    Widget? label,
    List<Widget> actions,
    List<Widget> operate,
    int? count,
    int? timestamp,
    void Function()? onTap,
  }) = _CardWithChat;

  factory CustomCard.icon({
    Key? key,
    required Widget icon,
    Widget? text,
    Widget? label,
    Color? borderColor,
    Axis direction,
    void Function()? onTap,
  }) = _CardWithIcon;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      // behavior的HitTestBehavior 值有三个：HitTestBehavior.opaque、 HitTestBehavior.deferToChild、HitTestBehavior.translucent
      // HitTestBehavior.opaque 自己处理事件
      // HitTestBehavior.deferToChild child处理事件
      // HitTestBehavior.translucent 自己和child都可以接收事件
      behavior: HitTestBehavior.translucent,
      onTap: onTap,
      child: Container(
        width: width,
        height: height,
        // 组件内容边缘的切割方式
        clipBehavior: clipBehavior,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.all(Radius.circular(20.w)),
          border: showBorder
              ? Border.all(
                  color: borderColor ?? Theme.of(context).colorScheme.outline,
                  width: 1.w,
                )
              : null,
          gradient: gradientColors != null
              ? LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: gradientColors!,
                )
              : null,
          boxShadow: [
            BoxShadow(
              color: shadowColor ?? Theme.of(context).colorScheme.shadow,
              offset: Offset(0, 20.w),
              blurRadius: 10.w,
              spreadRadius: -10.w,
            ),
          ],
        ),
        child: child,
      ),
    );
  }
}

class _CardCommon extends CustomCard {
  final Widget title;
  final Widget? subtitle;
  final Widget? label;
  final Widget? iconText;
  final Widget? bottom;
  final Widget? tag;
  final Widget? toggleIcon;
  final Widget? image;

  _CardCommon({
    Key? key,
    required this.title,
    this.subtitle,
    this.label,
    this.iconText,
    this.bottom,
    this.tag,
    this.toggleIcon,
    this.image,
    void Function()? onTap,
  }) : super(
          key: key,
          onTap: onTap,
          child: _CardCommonChild(
            image: image,
            title: title,
            subtitle: subtitle,
            label: label,
            value: iconText,
            bottom: bottom,
            tag: tag,
            toggleIcon: toggleIcon,
          ),
        );
}

class _CardCommonChild extends StatelessWidget {
  final Widget title;
  final Widget? subtitle;
  final Widget? label;
  final Widget? value;
  final Widget? bottom;
  final Widget? tag;
  final Widget? toggleIcon;
  final Widget? image;

  const _CardCommonChild({
    Key? key,
    required this.title,
    this.subtitle,
    this.label,
    this.value,
    this.toggleIcon,
    this.image,
    this.bottom,
    this.tag,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    var body = <Widget>[];
    if (subtitle != null || label != null) {
      body.add(SizedBox(height: label == null ? 10.w : 5.w));
      if (subtitle != null) body.add(_buildSubtitle(colorScheme));
      body.add(SizedBox(height: subtitle == null ? 0 : 3.w));
      if (label != null) body.add(_buildLabel(colorScheme));
    }
    if (value != null) {
      body = [
        Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: body,
              ),
            ),
            _buildValue(colorScheme),
          ],
        ),
      ];
    }

    return Stack(
      children: [
        Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Row(
                children: [
                  if (image != null)
                    SizedBox(
                      width: 60,
                      height: 60,
                      child: image,
                    ),
                  SizedBox(width: 16.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(
                            right: toggleIcon != null ? 16.w + 24.w : 0,
                          ),
                          child: _buildTitle(colorScheme),
                        ),
                        ...body,
                      ],
                    ),
                  ),
                ],
              ),
              if (bottom != null) Divider(height: 32.w),
              if (bottom != null) bottom!,
            ],
          ),
        ),
        if (toggleIcon != null)
          Positioned(
            right: 0,
            top: 0,
            child: _buildCollect(colorScheme),
          ),
      ],
    );
  }

  Widget _buildTitle(ColorScheme colorScheme) {
    Widget text = DefaultTextStyle.merge(
      style: TextStyle(
        
        fontSize: subtitle == null || label == null ? 20.w : 18.w,
        color: colorScheme.onSurface,
      ),
      child: title,
    );
    if (tag == null) return text;
    text = Expanded(child: text);
    text = Row(
      children: [
        text,
        DefaultTextStyle.merge(
          style: TextStyle(
            fontSize: 14.w,
            
            height: 1.3,
          ),
          child: tag!,
        ),
      ],
    );
    return text;
  }

  Widget _buildSubtitle(ColorScheme colorScheme) {
    if (subtitle == null) return const SizedBox.shrink();
    return DefaultTextStyle.merge(
      style: TextStyle(
        // 设置字体粗细
        
        fontSize: label == null ? 16.w : 13.w,
        color: colorScheme.onSurface.withValues(red: 0, green: 0, blue: 0, alpha: 0.9),
      ),
      child: subtitle!,
    );
  }

  Widget _buildLabel(ColorScheme colorScheme) {
    if (label == null) return const SizedBox.shrink();
    return DefaultTextStyle.merge(
      style: TextStyle(
        fontSize: subtitle == null ? 13.w : 11.w,
        color: colorScheme.onSurface.withValues(red: 0, green: 0, blue: 0, alpha: 0.5),
      ),
      child: label!,
    );
  }

  Widget _buildValue(ColorScheme colorScheme) {
    if (value == null) return const SizedBox.shrink();
    return DefaultTextStyle.merge(
      style: TextStyle(
        
        fontSize: 18.w,
        color: colorScheme.primary,
      ),
      child: value!,
    );
  }

  Widget _buildCollect(ColorScheme colorScheme) {
    if (toggleIcon == null) return const SizedBox.shrink();
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: toggleIcon,
    );
  }
}

class _CardWithChat extends CustomCard {
  final Widget nickname;
  final Widget? label;
  final String? avatar;
  final List<Widget> actions;
  final List<Widget> operate;
  final int? count;
  final int? timestamp;

  _CardWithChat({
    Key? key,
    required this.nickname,
    this.avatar,
    this.label,
    this.actions = const [],
    this.operate = const [],
    this.count,
    this.timestamp,
    void Function()? onTap,
  }) : super(
          key: key,
          onTap: onTap,
          child: _CardWithChatChild(
            nickname: nickname,
            avatar: avatar,
            label: label,
            actions: actions,
            operate: operate,
            count: count,
            timestamp: timestamp,
          ),
        );
}

class _CardWithChatChild extends StatelessWidget {
  final Widget nickname;
  final String? avatar;
  final Widget? label;
  final List<Widget> actions;
  final List<Widget> operate;
  final int? count;
  final int? timestamp;

  const _CardWithChatChild({
    Key? key,
    required this.nickname,
    this.actions = const [],
    this.operate = const [],
    this.count,
    this.label,
    this.timestamp,
    this.avatar,
  }) : super(key: key);

  String get _time {
    final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp ?? 0);
    return '${dateTime.hour}:${dateTime.minute}';
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final rightActions = <Widget>[];
    final bottomOperate = <Widget>[];
    final body = <Widget>[];
    if (label != null) {
      body.add(SizedBox(height: 5.w));
      body.add(DefaultTextStyle.merge(
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
        style: TextStyle(
          fontSize: 16.w,
          color: colorScheme.onSurface.withValues(red: 0, green: 0, blue: 0, alpha: 0.7),
          height: 1.3,
        ),
        child: timestamp != null
            ? Row(
                children: [
                  Expanded(child: label!),
                  SizedBox(width: 12.w),
                  Text(_time),
                ],
              )
            : label!,
      ));
    }
    for (var element in actions) {
      rightActions.add(SizedBox(width: 16.w));
      rightActions.add(element);
    }
    for (var index = 0; index < operate.length; index++) {
      if (index != 0) bottomOperate.add(SizedBox(width: 16.w));
      bottomOperate.add(Expanded(child: operate[index]));
    }
    return Stack(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(vertical: 12.w, horizontal: 16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Row(
                children: [
                  OxAvatar.medium(url: avatar ?? ''),
                  SizedBox(width: 16.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(
                            right: (count ?? 0) > 0 ? 22.w + 12.w : 0,
                          ),
                          child: DefaultTextStyle.merge(
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              
                              fontSize: 20.w,
                              color: colorScheme.onSurface,
                              height: 1.3,
                            ),
                            child: nickname,
                          ),
                        ),
                        ...body,
                      ],
                    ),
                  ),
                  ...rightActions,
                ],
              ),
              if (operate.isNotEmpty) Divider(height: 32.w),
              Row(children: bottomOperate),
            ],
          ),
        ),
        if ((count ?? 0) > 0)
          Positioned(
            right: 16.w,
            top: 16.w,
            child: _buildCount(),
          ),
      ],
    );
  }

  Widget _buildCount() {
    return Container(
      width: 22.w,
      height: 22.w,
      alignment: Alignment.center,
      decoration: const ShapeDecoration(
        color: AppTheme.error,
        shape: CircleBorder(),
      ),
      child: Text(
        count! > 100 ? '99' : count.toString(),
        style: TextStyle(
          color: Colors.white,
          fontSize: 13.w,
          height: 1.3,
        ),
      ),
    );
  }
}

class _CardWithIcon extends CustomCard {
  final Widget icon;
  final Widget? text;
  final Widget? label;
  final Axis direction;

  _CardWithIcon({
    Key? key,
    required this.icon,
    this.text,
    this.label,
    this.direction = Axis.vertical,
    Color? borderColor,
    void Function()? onTap,
  }) : super(
          key: key,
          onTap: onTap,
          borderColor: borderColor,
          child: _CardWithIconChild(
            icon: icon,
            text: text,
            label: label,
            direction: direction,
          ),
        );
}

class _CardWithIconChild extends StatelessWidget {
  final Widget icon;
  final Widget? text;
  final Widget? label;
  final Axis direction;

  const _CardWithIconChild({
    Key? key,
    required this.icon,
    this.text,
    this.label,
    this.direction = Axis.vertical,
  }) : super(key: key);

  EdgeInsetsGeometry get _padding {
    if (direction == Axis.horizontal) return EdgeInsets.all(20.w);
    return EdgeInsets.symmetric(vertical: 24.w, horizontal: 16.w);
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    Widget child = Container(
      width: 64.w,
      height: 64.w,
      decoration: BoxDecoration(
        color: colorScheme.primary.withValues(red: 0, green: 0, blue: 0, alpha: 0.1),
        shape: BoxShape.circle,
      ),
      child: IconTheme(
        data: IconThemeData(color: colorScheme.primary, size: 32.w),
        child: icon,
      ),
    );
    final ws = [child];
    if (direction == Axis.vertical) {
      if (text != null || label != null) ws.add(SizedBox(height: 12.w));
      if (text != null) {
        ws.add(DefaultTextStyle.merge(
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 15.w),
          child: text!,
        ));
      }
      if (text != null && label != null) ws.add(SizedBox(height: 6.w));
      if (label != null) {
        ws.add(DefaultTextStyle.merge(
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 15.w,
            color: colorScheme.onSurface.withValues(red: 0, green: 0, blue: 0, alpha: 0.7),
          ),
          child: label!,
        ));
      }
      child = Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: ws,
      );
    } else {
      Widget? textChild;
      Widget? labelChild;
      if (text != null) {
        textChild = DefaultTextStyle.merge(
          style: TextStyle(
            fontSize: 18.w,
            
          ),
          child: text!,
        );
      }
      if (label != null) {
        labelChild = DefaultTextStyle.merge(
          style: TextStyle(
            fontSize: 15.w,
            color: colorScheme.onSurface.withValues(red: 0, green: 0, blue: 0, alpha: 0.7),
          ),
          child: label!,
        );
      }
      if (text != null || label != null) ws.add(SizedBox(width: 20.w));
      if (textChild != null && labelChild != null) {
        ws.add(Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [labelChild, SizedBox(height: 6.w), textChild],
          ),
        ));
      } else if (textChild != null) {
        ws.add(Expanded(child: textChild));
      } else if (labelChild != null) {
        ws.add(Expanded(child: labelChild));
      }
      child = Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: ws,
      );
    }
    return Padding(padding: _padding, child: child);
  }
}
