import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cheestack_flt/shared/utils/index.dart';
import 'package:google_fonts/google_fonts.dart';




abstract class AppTheme {
  // ==================== 尺寸系统 ====================

  // 字体大小（响应式）
  static double get fontSmall => 12.sp; // 小字体（提示文本）
  static double get fontBody => 14.sp; // 正文字体（主要内容）
  static double get fontTitle => 16.sp; // 标题字体（表单标题）
  static double get fontLarge => 20.sp; // 大标题字体（页面标题）

  // 间距大小（响应式）
  static REdgeInsets get paddingSmall => REdgeInsets.all(8); // 小间距（图标周围）
  static REdgeInsets get paddingMedium => REdgeInsets.all(16); // 标准间距（组件内部）
  static REdgeInsets get paddingLarge => REdgeInsets.all(24); // 大间距（组件之间）

  // 组件尺寸（响应式）
  static double get buttonHeight => 44.h; // 按钮高度（标准触摸目标）
  static double get inputHeight => 48.h; // 输入框高度（舒适输入）

  // 间距高度（响应式）
  static double get spacingSmall => 16.h; // 组件间距（紧凑）
  static double get spacingMedium => 24.h; // 区块间距（标准）
  static double get spacingLarge => 32.h; // 大区块间距

  // 圆角大小（响应式）
  static double get borderRadiusLarge => 28.w;
  static double get borderRadiusMedium => 20.w;
  static double get borderRadiusSmall => 12.w;

  // 兼容性属性
  static double get margin => paddingMedium.left; // 16.0
  static double get marginS => paddingSmall.left; // 8.0

  // 响应式间距策略
  /// 根据设备类型返回不同的间距
  /// [isTablet] 是否为平板设备
  /// [mobileSize] 手机端尺寸
  /// [tabletSize] 平板端尺寸
  static double responsiveSpacing(
      bool isTablet, double mobileSize, double tabletSize) {
    return isTablet ? tabletSize : mobileSize;
  }

  /// 响应式字体大小
  static double responsiveFontSize(
      bool isTablet, double mobileSize, double tabletSize) {
    return isTablet ? tabletSize : mobileSize;
  }

  /// 响应式内边距
  static REdgeInsets responsivePadding(
      bool isTablet, double mobileSize, double tabletSize) {
    return REdgeInsets.all(isTablet ? tabletSize : mobileSize);
  }

  // ==================== 颜色系统 ====================

  /// 主体颜色 - 使用明亮的紫色作为种子颜色
  static const primary = Color(0xFF8B5CF6); // 明亮的紫色

  /// 图片中的主题色 - 使用天蓝色作为主色调
  static const mint = Color(0xFF38BDF8); // 天蓝色

  /// 成功颜色
  static const success = Color(0xFF4ADE80); // 明亮的绿色

  /// 警告颜色
  static const warning = Color(0xFFFBBF24); // 明亮的黄色

  /// 错误颜色
  static const error = Color(0xFFF87171); // 柔和的红色

  /// 信息颜色
  static const info = Color(0xFF60A5FA); // 明亮的蓝色

  /// 日历选中日期的颜色
  static const calendarSelectedDay = Color(0xFF8B5CF6); // 明亮的紫色
  static const calendarSelectedDayText = Colors.white;

  /// 主题模式, 默认为跟随系统
  /// @ThemeMode.system：表示使用系统设置的主题模式。
  /// @ThemeMode.light：表示使用浅色主题模式，即应用程序的主题会使用明亮的颜色和背景。
  /// @ThemeMode.dark：表示使用深色主题模式，即应用程序的主题会使用暗色的颜色和背景。
  static ThemeMode mode = ThemeMode.system;

  /// 默认的系统状态栏和导航栏样式, SystemUiOverlayStyle 是一个用于定义系统状态栏和导航栏样式的类。
  /// 通过设置不同的属性，可以定制状态栏和导航栏的颜色、文字颜色等
  static SystemUiOverlayStyle get systemStyle => const SystemUiOverlayStyle(
        /// 状态栏的背景颜色。在这里设置为透明，意味着状态栏会根据页面内容的背景颜色进行自动调整
        statusBarColor: Colors.transparent,

        /// 状态栏的亮度。在这里设置为 Brightness.light，表示状态栏的文字和图标使用浅色。
        statusBarBrightness: Brightness.light,

        /// 状态栏的图标亮度。在这里设置为 Brightness.dark，表示状态栏的图标使用深色。
        statusBarIconBrightness: Brightness.dark,

        /// 导航栏和内容之间的分割线颜色。在这里设置为透明，意味着没有分割线。
        systemNavigationBarDividerColor: Colors.transparent,

        /// 导航栏的背景颜色。在这里设置为白色，表示导航栏的背景为白色。
        systemNavigationBarColor: Colors.white,

        /// 导航栏的图标亮度。在这里设置为 Brightness.dark，表示导航栏的图标使用深色。
        systemNavigationBarIconBrightness: Brightness.dark,
      );

  /// 浅色模式下的系统状态栏和导航栏样式
  static SystemUiOverlayStyle get systemStyleLight => systemStyle.copyWith(
        statusBarBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarIconBrightness: Brightness.dark,
      );

  /// 深色模式下的系统状态栏和导航栏样式
  static SystemUiOverlayStyle get systemStyleDark => systemStyle.copyWith(
        statusBarBrightness: Brightness.dark,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: const Color(0xFF0D0D0D),
        systemNavigationBarIconBrightness: Brightness.light,
      );

  /// 根据当前主题模式（mode）来设置系统状态栏和导航栏的样式。
  /// 在 ThemeMode.system 模式下，会根据设备的亮暗模式来选择浅色或深色样式
  static void setSystemStyle() {
    switch (mode) {
      case ThemeMode.system:
        if (Screen.mediaQuery.platformBrightness == Brightness.dark) {
          SystemChrome.setSystemUIOverlayStyle(systemStyleDark);
        } else {
          SystemChrome.setSystemUIOverlayStyle(systemStyleLight);
        }
        break;
      case ThemeMode.light:
        SystemChrome.setSystemUIOverlayStyle(systemStyleLight);
        break;
      case ThemeMode.dark:
        SystemChrome.setSystemUIOverlayStyle(systemStyleDark);
        break;
    }
  }

  /// 返回浅色主题
  static ThemeData light() {
    // 使用种子颜色生成整个配色方案 - 适合儿童的明亮色彩
    var scheme = ColorScheme.fromSeed(
      seedColor: primary, // 使用明亮的紫色作为种子颜色
      brightness: Brightness.light,
      // 自定义颜色以创建更活泼的儿童友好配色方案
      primary: primary,
      secondary: Color(0xFFFFA726), // 明亮的橙色
      tertiary: Color(0xFF4AEDC4), // 亮松石绿
      // 使背景色更柔和
      surface: Colors.white,
      // 确保对比度足够
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onTertiary: Color(0xFF1F2937), // 深灰色文本
      onSurface: Color(0xFF1F2937), // 深灰色文本
    );

    return _getTheme(scheme);
  }

  /// 返回深色主题
  static ThemeData dark() {
    // 使用种子颜色生成整个配色方案 - 适合儿童的深色主题
    var scheme = ColorScheme.fromSeed(
      seedColor: primary, // 使用明亮的紫色作为种子颜色
      brightness: Brightness.dark,
      // 自定义颜色以创建更活泼但不刺眼的儿童友好深色配色方案
      primary: Color(0xFFD8B4FE), // 浅紫色
      secondary: Color(0xFFFFD580), // 浅橙色
      tertiary: Color(0xFF5EEAD4), // 浅松石绿
      // 使背景色不那么暗，更友好
      surface: Color(0xFF374151), // 稍浅的表面
      // 确保对比度足够
      onPrimary: Color(0xFF1E293B), // 深色文本在浅色上
      onSecondary: Color(0xFF1E293B), // 深色文本在浅色上
      onTertiary: Color(0xFF1E293B), // 深色文本在浅色上
      onSurface: Color(0xFFF8FAFC), // 浅色文本
    );

    return _getTheme(scheme);
  }

  /// 创建 ThemeData 对象，它包含了应用程序的整体主题设置，包括颜色方案、背景颜色、字体样式、输入框样式等。
  static ThemeData _getTheme(ColorScheme scheme) {
    // 创建日历主题扩展 - 使用儿童友好的颜色
    final calendarThemeExt = CustomCalendarTheme(
      daySelectedBackgroundColor: primary, // 明亮的紫色
      daySelectedForegroundColor: Colors.white,
      todayBackgroundColor: primary.withValues(alpha: 0.15),
      todayForegroundColor: primary,
    );

    // 使用Google Fonts的Nunito字体，更圆润友好

    // 创建儿童友好的文本主题
    final textThemeM3 = TextTheme(
      displayLarge: GoogleFonts.nunito(
        fontSize: 57.w,
        fontWeight: FontWeight.w700,
        letterSpacing: -0.25,
      ),
      displayMedium: GoogleFonts.nunito(
        fontSize: 45.w,
        fontWeight: FontWeight.w700,
      ),
      displaySmall: GoogleFonts.nunito(
        fontSize: 36.w,
        fontWeight: FontWeight.w700,
      ),
      headlineLarge: GoogleFonts.nunito(
        fontSize: 32.w,
        fontWeight: FontWeight.w700,
      ),
      headlineMedium: GoogleFonts.nunito(
        fontSize: 28.w,
        fontWeight: FontWeight.w700,
      ),
      headlineSmall: GoogleFonts.nunito(
        fontSize: 24.w,
        fontWeight: FontWeight.w700,
      ),
      titleLarge: GoogleFonts.nunito(
        fontSize: 22.w,
        fontWeight: FontWeight.w700,
        letterSpacing: 0,
      ),
      titleMedium: GoogleFonts.nunito(
        fontSize: 16.w,
        fontWeight: FontWeight.w700,
        letterSpacing: 0.15,
      ),
      titleSmall: GoogleFonts.nunito(
        fontSize: 14.w,
        
        letterSpacing: 0.1,
      ),
      bodyLarge: GoogleFonts.nunito(
        fontSize: 16.w,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.5,
      ),
      bodyMedium: GoogleFonts.nunito(
        fontSize: 14.w,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.25,
      ),
      bodySmall: GoogleFonts.nunito(
        fontSize: 12.w,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.4,
      ),
      labelLarge: GoogleFonts.nunito(
        fontSize: 14.w,
        
        letterSpacing: 0.1,
      ),
      labelMedium: GoogleFonts.nunito(
        fontSize: 12.w,
        
        letterSpacing: 0.5,
      ),
      labelSmall: GoogleFonts.nunito(
        fontSize: 11.w,
        
        letterSpacing: 0.5,
      ),
    );

    return ThemeData(
      useMaterial3: true,
      colorScheme: scheme,
      scaffoldBackgroundColor: scheme.surface,
      textTheme: textThemeM3,

      // 添加主题扩展
      extensions: [calendarThemeExt],

      /// 用于在用户点击按钮或类似可点击元素时(例如`InkWell`)，显示的水波纹效果的颜色
      splashColor: scheme.primary.withValues(alpha: 0.1),

      /// 用于在用户长按按钮或类似可点击元素时(例如`InkWell`)，显示的水波纹效果的颜色
      highlightColor: scheme.primary.withValues(alpha: 0.05),

      // 设置AppBar主题 - 儿童友好风格
      appBarTheme: AppBarTheme(
        elevation: 0,
        scrolledUnderElevation: 0, // 禁用滚动时的阴影效果
        surfaceTintColor: Colors.transparent, // 禁用表面着色效果
        shadowColor: Colors.transparent, // 禁用阴影颜色
        centerTitle: true,
        iconTheme:
            IconThemeData(color: scheme.primary, size: fontLarge), // 使用主题字体大小
        titleTextStyle: GoogleFonts.nunito(
          color: scheme.primary, // 使用主题色
          fontSize: fontLarge, // 使用主题字体大小
          fontWeight: FontWeight.w700,
        ),
      ),

      // 设置按钮主题 - 儿童友好风格
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: scheme.primary,
          foregroundColor: scheme.onPrimary,
          elevation: 2, // 增加一点阴影，让按钮看起来更有趣
          padding: EdgeInsets.symmetric(
              horizontal: paddingMedium.left, vertical: paddingSmall.top),
          minimumSize: Size(double.infinity, buttonHeight), // 使用主题按钮高度
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadiusLarge), // 使用主题圆角
          ),
          textStyle: TextStyle(fontWeight: FontWeight.w500),
        ),
      ),

      filledButtonTheme: FilledButtonThemeData(
        style: FilledButton.styleFrom(
          backgroundColor: scheme.secondary, // 使用橙色作为填充按钮
          foregroundColor: scheme.onSecondary,
          padding: EdgeInsets.symmetric(
              horizontal: paddingMedium.left, vertical: paddingSmall.top),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadiusLarge), // 使用主题圆角
          ),
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: scheme.primary,
          side: BorderSide(color: scheme.primary, width: 2), // 更粗的边框
          padding: EdgeInsets.symmetric(
              horizontal: paddingMedium.left, vertical: paddingSmall.top),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadiusLarge), // 使用主题圆角
          ),
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: scheme.primary,
          padding: EdgeInsets.symmetric(
              horizontal: paddingSmall.left, vertical: paddingSmall.top),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadiusMedium),
          ),
        ),
      ),

      // 设置卡片主题 - 儿童友好风格
      cardTheme: CardTheme(
        elevation: 3, // 增加阴影，让卡片看起来更有趣
        shadowColor: scheme.primary.withValues(alpha: 0.3), // 带有主题色的阴影
        color: scheme.surface,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusLarge),
        ),
        margin: EdgeInsets.symmetric(
            horizontal: paddingMedium.left, vertical: paddingSmall.top),
      ),

      // 设置输入框主题 - 儿童友好风格
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: scheme.surfaceContainerHighest.withValues(alpha: 0.5),
        contentPadding: EdgeInsets.symmetric(
            horizontal: paddingMedium.left, vertical: paddingMedium.top),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusLarge), // 使用主题圆角
          borderSide: BorderSide(color: scheme.outline, width: 1),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusLarge),
          borderSide: BorderSide(
              color: scheme.primary.withValues(alpha: 0.5),
              width: 1.5), // 带颜色的边框
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusLarge),
          borderSide: BorderSide(color: scheme.primary, width: 2.5), // 更粗的边框
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusLarge),
          borderSide: BorderSide(color: scheme.error, width: 1.5),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusLarge),
          borderSide: BorderSide(color: scheme.error, width: 2.5),
        ),
        hintStyle:
            TextStyle(color: scheme.onSurfaceVariant.withValues(alpha: 0.7)),
        prefixIconColor: scheme.primary, // 使用主题色
        suffixIconColor: scheme.primary,
      ),

      // 设置底部导航栏主题 - 儿童友好风格
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: scheme.surface,
        selectedItemColor: scheme.primary,
        unselectedItemColor: scheme.onSurfaceVariant.withValues(alpha: 0.7),
        elevation: 8, // 增加阴影
        type: BottomNavigationBarType.fixed,
      ),

      // 设置NavigationBar主题 - 儿童友好风格
      navigationBarTheme: NavigationBarThemeData(
        backgroundColor: scheme.surface,
        indicatorColor: scheme.primary.withValues(alpha: 0.2),
        labelTextStyle: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return GoogleFonts.nunito(
              color: scheme.primary,
              fontSize: fontBody, // 使用主题字体大小
              fontWeight: FontWeight.w700,
            );
          }
          return GoogleFonts.nunito(
            color: scheme.onSurfaceVariant,
            fontSize: fontBody,
            
          );
        }),
        iconTheme: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return IconThemeData(
              color: scheme.primary,
              size: fontLarge, // 使用主题字体大小
            );
          }
          return IconThemeData(
            color: scheme.onSurfaceVariant.withValues(alpha: 0.7),
            size: fontTitle,
          );
        }),
        height: spacingLarge * 2, // 使用主题间距
      ),

      // 设置浮动按钮主题 - 儿童友好风格
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: scheme.secondary, // 使用橙色
        foregroundColor: scheme.onSecondary,
        elevation: 6, // 更大的阴影
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusLarge + 2), // 完全圆形
        ),
        extendedPadding: EdgeInsets.symmetric(
            horizontal: paddingMedium.left, vertical: paddingSmall.top),
        extendedTextStyle: GoogleFonts.poppins(
          fontSize: fontTitle,
          fontWeight: FontWeight.w500,
        ),
      ),

      // 设置Chip主题 - 儿童友好风格
      chipTheme: ChipThemeData(
        backgroundColor: scheme.primary.withValues(alpha: 0.1),
        selectedColor: scheme.primary,
        labelStyle: GoogleFonts.nunito(
          fontSize: fontBody,
          
        ),
        padding: EdgeInsets.symmetric(
            horizontal: paddingMedium.left,
            vertical: paddingSmall.top), // 使用主题间距
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium), // 使用主题圆角
        ),
        side: BorderSide(color: scheme.primary.withValues(alpha: 0.3)), // 添加边框
      ),

      // 设置Switch主题 - 儿童友好风格
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return scheme.primary; // 使用主题色
          }
          return Colors.white;
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return scheme.primary.withValues(alpha: 0.5); // 半透明主题色
          }
          return scheme.surfaceContainerHighest;
        }),
        trackOutlineColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return Colors.transparent;
          }
          return scheme.outline;
        }),
        thumbIcon: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return Icon(Icons.check,
                color: Colors.white, size: fontSmall); // 使用主题字体大小
          }
          return null;
        }),
      ),

      // 设置分割线主题
      dividerTheme: DividerThemeData(
        color: scheme.primary.withValues(alpha: 0.1), // 使用主题色
        thickness: 1,
        space: 1,
      ),

      // 设置对话框主题 - 儿童友好风格
      dialogTheme: DialogTheme(
        backgroundColor: scheme.surface,
        elevation: 8, // 更大的阴影
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusLarge),
        ),
        titleTextStyle: GoogleFonts.nunito(
          fontSize: fontLarge,
          fontWeight: FontWeight.w700,
          color: scheme.primary, // 使用主题色
        ),
        contentTextStyle: GoogleFonts.nunito(
          fontSize: fontTitle,
          fontWeight: FontWeight.w500,
          color: scheme.onSurface,
        ),
      ),

      // 设置滑块主题 - 儿童友好风格
      sliderTheme: SliderThemeData(
        activeTrackColor: scheme.primary,
        inactiveTrackColor: scheme.primary.withValues(alpha: 0.2),
        thumbColor: scheme.secondary, // 使用橙色
        overlayColor: scheme.primary.withValues(alpha: 0.12),
        trackHeight: 6, // 更粗的轨道
        thumbShape: RoundSliderThumbShape(
            enabledThumbRadius: paddingSmall.left), // 使用主题尺寸
      ),

      // 设置图标主题
      iconTheme: IconThemeData(color: scheme.onSurface),

      // 设置ListTile主题 - Material 3风格
      listTileTheme: ListTileThemeData(
        contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.w),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusSmall.w),
        ),
        tileColor: scheme.surface,
      ),

      // Material 3 特有组件样式

      // Badge 样式 - 儿童友好风格
      badgeTheme: BadgeThemeData(
        backgroundColor: scheme.error,
        textColor: Colors.white,
        largeSize: 24.w, // 更大的徽章
        smallSize: 8.w,
        padding: EdgeInsets.all(4.w),
      ),

      // SegmentedButton 样式 - 儿童友好风格
      segmentedButtonTheme: SegmentedButtonThemeData(
        style: ButtonStyle(
          backgroundColor: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.selected)) {
              return scheme.primary;
            }
            return scheme.surface;
          }),
          foregroundColor: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.selected)) {
              return Colors.white;
            }
            return scheme.primary;
          }),
          shape: WidgetStateProperty.all(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadiusMedium.w),
              side: BorderSide(color: scheme.primary), // 添加边框
            ),
          ),
          padding: WidgetStateProperty.all(
            EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.w), // 更大的内边距
          ),
          textStyle: WidgetStateProperty.all(
            GoogleFonts.poppins(
              fontSize: 14.w,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),

      // NavigationDrawer 样式 - 儿童友好风格
      drawerTheme: DrawerThemeData(
        backgroundColor: scheme.surface,
        scrimColor: scheme.primary.withValues(alpha: 0.2), // 使用主题色
        elevation: 8, // 更大的阴影
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(borderRadiusLarge),
            bottomRight: Radius.circular(borderRadiusLarge),
          ),
        ),
      ),

      // NavigationRail 样式 - 儿童友好风格
      navigationRailTheme: NavigationRailThemeData(
        backgroundColor: scheme.surface,
        selectedIconTheme: IconThemeData(
          color: scheme.primary,
          size: fontLarge, // 使用主题字体大小
        ),
        unselectedIconTheme: IconThemeData(
          color: scheme.onSurfaceVariant.withValues(alpha: 0.7),
          size: fontTitle,
        ),
        selectedLabelTextStyle: GoogleFonts.nunito(
          fontSize: fontBody,
          fontWeight: FontWeight.w700,
          color: scheme.primary,
        ),
        unselectedLabelTextStyle: GoogleFonts.nunito(
          fontSize: fontBody,
          
          color: scheme.onSurfaceVariant.withValues(alpha: 0.7),
        ),
        indicatorColor: scheme.primary.withValues(alpha: 0.2),
        useIndicator: true,
      ),

      // BottomSheet 样式 - Material 3
      bottomSheetTheme: BottomSheetThemeData(
        backgroundColor: scheme.surface,
        modalBackgroundColor: scheme.surface,
        elevation: 1,
        modalElevation: 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(borderRadiusLarge),
          ),
        ),
      ),

      // Snackbar 样式 - 儿童友好风格
      snackBarTheme: SnackBarThemeData(
        backgroundColor: scheme.primary,
        contentTextStyle: GoogleFonts.nunito(
          color: Colors.white,
          fontSize: fontTitle,
          
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium), // 使用主题圆角
        ),
        behavior: SnackBarBehavior.floating,
        elevation: 6, // 更大的阴影
      ),

      // Menu 样式 - Material 3
      menuTheme: MenuThemeData(
        style: MenuStyle(
          backgroundColor: WidgetStateProperty.all(scheme.surface),
          elevation: WidgetStateProperty.all(3),
          shape: WidgetStateProperty.all(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadiusSmall),
            ),
          ),
        ),
      ),

      // MenuBar 样式 - Material 3
      menuBarTheme: MenuBarThemeData(
        style: MenuStyle(
          backgroundColor: WidgetStateProperty.all(scheme.surface),
          elevation: WidgetStateProperty.all(3),
          shape: WidgetStateProperty.all(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadiusSmall),
            ),
          ),
        ),
      ),

      // MenuButton 样式 - Material 3
      menuButtonTheme: MenuButtonThemeData(
        style: ButtonStyle(
          backgroundColor: WidgetStateProperty.all(Colors.transparent),
          foregroundColor: WidgetStateProperty.all(scheme.onSurface),
          overlayColor: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.pressed)) {
              return scheme.onSurface.withValues(alpha: 0.12);
            }
            if (states.contains(WidgetState.hovered)) {
              return scheme.onSurface.withValues(alpha: 0.08);
            }
            if (states.contains(WidgetState.focused)) {
              return scheme.onSurface.withValues(alpha: 0.12);
            }
            return null;
          }),
        ),
      ),
    );
  }
}

/// 自定义日历主题扩展
class CustomCalendarTheme extends ThemeExtension<CustomCalendarTheme> {
  final Color? daySelectedBackgroundColor;
  final Color? daySelectedForegroundColor;
  final Color? todayBackgroundColor;
  final Color? todayForegroundColor;

  CustomCalendarTheme({
    this.daySelectedBackgroundColor,
    this.daySelectedForegroundColor,
    this.todayBackgroundColor,
    this.todayForegroundColor,
  });

  @override
  ThemeExtension<CustomCalendarTheme> copyWith({
    Color? daySelectedBackgroundColor,
    Color? daySelectedForegroundColor,
    Color? todayBackgroundColor,
    Color? todayForegroundColor,
  }) {
    return CustomCalendarTheme(
      daySelectedBackgroundColor:
          daySelectedBackgroundColor ?? this.daySelectedBackgroundColor,
      daySelectedForegroundColor:
          daySelectedForegroundColor ?? this.daySelectedForegroundColor,
      todayBackgroundColor: todayBackgroundColor ?? this.todayBackgroundColor,
      todayForegroundColor: todayForegroundColor ?? this.todayForegroundColor,
    );
  }

  @override
  ThemeExtension<CustomCalendarTheme> lerp(
    covariant ThemeExtension<CustomCalendarTheme>? other,
    double t,
  ) {
    if (other is! CustomCalendarTheme) {
      return this;
    }
    return CustomCalendarTheme(
      daySelectedBackgroundColor: Color.lerp(
          daySelectedBackgroundColor, other.daySelectedBackgroundColor, t),
      daySelectedForegroundColor: Color.lerp(
          daySelectedForegroundColor, other.daySelectedForegroundColor, t),
      todayBackgroundColor:
          Color.lerp(todayBackgroundColor, other.todayBackgroundColor, t),
      todayForegroundColor:
          Color.lerp(todayForegroundColor, other.todayForegroundColor, t),
    );
  }
}

/// 扩展ThemeData以便于获取自定义日历主题
extension CalendarThemeExtension on ThemeData {
  CustomCalendarTheme get calendarTheme =>
      extension<CustomCalendarTheme>() ?? CustomCalendarTheme();
}
