library upload_audio;

import 'dart:async';

import 'package:cheestack_flt/common/data/index.dart';
import 'package:cheestack_flt/controllers/index.dart';
import 'package:cheestack_flt/shared/utils/index.dart';

import 'package:cheestack_flt/i18n/index.dart';
import 'package:flutter/material.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:flutter_sound/public/flutter_sound_recorder.dart';
import 'package:get/get.dart';
import 'package:file_picker/file_picker.dart';

part 'controller.dart';
part 'view.dart';
