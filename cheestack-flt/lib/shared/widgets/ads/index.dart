library ads;

import 'dart:io';

import 'package:cheestack_flt/shared/utils/index.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gromore/callback/gromore_banner_callback.dart';
import 'package:flutter_gromore/callback/gromore_interstitial_callback.dart';
import 'package:flutter_gromore/callback/gromore_reward_callback.dart';
import 'package:flutter_gromore/callback/gromore_splash_callback.dart';
import 'package:flutter_gromore/config/gromore_interstitial_config.dart';
import 'package:flutter_gromore/config/gromore_reward_config.dart';
import 'package:flutter_gromore/config/gromore_splash_config.dart';
import 'package:flutter_gromore/flutter_gromore.dart';
import 'package:flutter_gromore/view/gromore_banner_view.dart';

part 'ads.dart';
part 'banner.dart';
part 'configs.dart';
