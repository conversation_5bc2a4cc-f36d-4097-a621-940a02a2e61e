part of swidgets;

class SupgradeController extends GetxController {
  static SupgradeController get to => Get.find();
  bool isForceUpdate = false;
  bool hasUpdate = false;
  OtaEvent? currentEvent;
  ReleaseModel release = ReleaseModel();
  bool isUpdating = false;
  String savePath = "";
  PackageInfo packageInfo = PackageInfo(
      appName: 'cheestack',
      packageName: 'com.chivetech.cheestack',
      version: '0.0.0',
      buildNumber: '0');
  double downloadPercent = 0;
  bool isDownloading = false;
  String status = "";
  String appStoreId = "6464110936";
  bool isShowingDialog = false;

  initData() async {
    packageInfo = await PackageInfo.fromPlatform();
  }

  @override
  void onReady() async {
    await initData();
    super.onReady();
  }

  Future checkUpdate() async {
    /// 获取最新版本信息
    ReleaseModel? srvVersion = await ApiRealese.get();
    if (srvVersion == null) return false;
    release = srvVersion;
    // 获取当前版本信息
    packageInfo = await PackageInfo.fromPlatform();

    /// 根据平台类型判断是否有更新
    if (Platform.isIOS) {
      // Console.log("ios");
      hasUpdate = SversionCheck.checkVersion(
          release.version ?? "0.0.0", packageInfo.version);
      isForceUpdate = SversionCheck.checkVersion(
          release.minVersion ?? "0.0.0", packageInfo.version);
    } else if (Platform.isAndroid) {
      hasUpdate = SversionCheck.checkBuild(
          release.build ?? 0, int.parse(packageInfo.buildNumber));
      isForceUpdate = SversionCheck.checkBuild(
          release.minBuild ?? 0, int.parse(packageInfo.buildNumber));
    }
    // Console.log(hasUpdate);
    if (hasUpdate) {
      checSkipUpgradeTime();
    }
    update();
  }

  void checSkipUpgradeTime() {
    var skipUpgradeTime = StorageService.to.getJSON(CacheKey.skipUpgradeTime);
    if (skipUpgradeTime is Map<String, dynamic>) {
      if (Platform.isIOS) {
        if (skipUpgradeTime.get("version") != release.version) {
          showUpdateDialog();
        } else {
          DateTime dateTime = DateTime.parse(skipUpgradeTime.get("time"));
          if (DateTime.now().toUtc().difference(dateTime).inSeconds > 10) {
            showUpdateDialog();
          }
        }
      } else if (Platform.isAndroid) {
        if (skipUpgradeTime.get("build") != release.build) {
          showUpdateDialog();
        } else {
          DateTime dateTime = DateTime.parse(skipUpgradeTime["time"]);
          if (DateTime.now().toUtc().difference(dateTime).inSeconds > 10) {
            showUpdateDialog();
          }
        }
      }
    } else {
      /// 本地没有数据时直接显示更新对话框
      showUpdateDialog();
    }
  }

  Future<void> showUpdateDialog() async {
    if (isShowingDialog) return;
    try {
      showDialog(
          context: Get.context!,
          barrierDismissible: false,
          builder: (context) {
            return PopScope(
                canPop: false,
                child: GetBuilder<SupgradeController>(
                    init: SupgradeController(),
                    builder: (controller) {
                      // 显示对话框
                      isShowingDialog = true;
                      return AlertDialog(
                        title: const Text("检测到新的版本"),
                        titleTextStyle:
                            const TextStyle(color: AppTheme.primary),
                        actionsAlignment: MainAxisAlignment.spaceAround,
                        actions: [
                          if (!isForceUpdate)
                            Sbutton(
                                size: SbuttonSize.small,
                                onPressed: isDownloading
                                    ? null
                                    : () async => setSkipUpgradeTime(),
                                child: const Text("稍候")),
                          Sbutton(
                            size: SbuttonSize.small,
                            onPressed: excuteUpdate,
                            child: const Text("升级"),
                          ),
                        ],
                        contentTextStyle:
                            const TextStyle(color: AppTheme.primary),
                        content: SizedBox(
                          height: 150,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              const SizedBox(height: 10),
                              isDownloading
                                  ? Text(status)
                                  : Text("有新的版本v${release.version}, \n是否更新?"),
                              const SizedBox(height: 10),
                              if (downloadPercent != 0)
                                LinearProgressIndicator(value: downloadPercent),
                              const SizedBox(height: 10),
                            ],
                          ),
                        ),
                      );
                    }));
          });
    } catch (e) {
      isShowingDialog = false;
      Console.log(e);
    }
  }

  Future setSkipUpgradeTime() async {
    await StorageService.to.setJSON(CacheKey.skipUpgradeTime, {
      "time": DateTime.now().toUtc().toString(),
      "version": release.version,
      "build": release.build
    });
    isShowingDialog = false;
    Get.back();
  }

  void excuteUpdate() {
    if (Platform.isIOS) {
      openAppStore();
    } else if (Platform.isAndroid) {
      Console.log("release.apkUrl: ${release.apkUrl}");
      if (!isDownloading) executeAndroidUpdate();
      isDownloading = true;
      update();
    }
  }

  Future<void> openAppStore() async {
    final Uri appStoreUrl =
        Uri.parse('https://apps.apple.com/app/id$appStoreId');

    if (await canLaunchUrl(appStoreUrl)) {
      await launchUrl(appStoreUrl);
      Get.back();
    } else {
      // 处理无法打开的情况
      Console.log('Could not launch $appStoreUrl');
    }
  }

  void executeAndroidUpdate() async {
    try {
      Console.log("release.apkUrl: ${release.apkUrl}");
      await OtaUpdate().cancel();
      OtaUpdate()
          .execute(
        release.apkUrl ?? "",
        destinationFilename: 'cheestack-${release.version}.apk',
      )
          .listen((OtaEvent event) {
        handleOtaEvent(event);
      });
    } catch (e) {
      Console.log(e);
      isDownloading = false;
      update();
    }
  }

  void handleOtaEvent(OtaEvent event) {
    switch (event.status) {
      case OtaStatus.DOWNLOADING:
        downloadPercent = double.parse(event.value ?? "0") * 0.01;
        status = '下载中...';
        isDownloading = true;
        update();
        break;
      case OtaStatus.INSTALLING:
        status = '安装中...';
        isDownloading = false;
        update();
        break;
      case OtaStatus.ALREADY_RUNNING_ERROR:
      case OtaStatus.PERMISSION_NOT_GRANTED_ERROR:
      case OtaStatus.INTERNAL_ERROR:
      case OtaStatus.DOWNLOAD_ERROR:
      case OtaStatus.CHECKSUM_ERROR:
      case OtaStatus.CANCELED:
        status = _getErrorMessage(event.status);
        isDownloading = false;
        update();
        break;
    }
  }

  String _getErrorMessage(OtaStatus status) {
    switch (status) {
      case OtaStatus.ALREADY_RUNNING_ERROR:
        return '已在运行';
      case OtaStatus.PERMISSION_NOT_GRANTED_ERROR:
        return '权限不足';
      case OtaStatus.INTERNAL_ERROR:
        return '网络错误';
      case OtaStatus.DOWNLOAD_ERROR:
        return '下载失败';
      case OtaStatus.CHECKSUM_ERROR:
        return '校验失败';
      case OtaStatus.CANCELED:
        return '正在准备...';
      default:
        return '未知错误';
    }
  }
}
