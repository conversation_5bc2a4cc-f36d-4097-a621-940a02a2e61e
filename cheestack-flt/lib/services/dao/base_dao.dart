import 'package:sqflite/sqflite.dart';
import 'package:uuid/uuid.dart';

/// 基础数据访问对象类
/// 提供通用的CRUD操作方法
abstract class BaseDao<T> {
  /// 表名
  String get tableName;
  
  /// 主键名
  String get primaryKey;
  
  /// 是否使用自增主键
  bool get useAutoIncrement => true;
  
  /// 获取数据库实例
  Database get db => DatabaseService.to.database;
  
  /// 从Map创建模型对象
  T fromMap(Map<String, dynamic> map);
  
  /// 将模型对象转换为Map
  Map<String, dynamic> toMap(T entity);
  
  /// 添加同步记录
  Future<void> addSyncRecord(String action, String recordId, Map<String, dynamic>? dataBefore, Map<String, dynamic> dataAfter) async {
    final userId = dataAfter['user_id'] ?? '';
    final deviceId = await _getDeviceId();
    
    await db.insert('sync_records', {
      'id': const Uuid().v4(),
      'user_id': userId,
      'device_id': deviceId,
      'table_name': tableName,
      'record_id': recordId,
      'action': action,
      'status': 'pending',
      'client_version': 1,
      'server_version': 0,
      'data_before': dataBefore != null ? _encodeJson(dataBefore) : null,
      'data_after': _encodeJson(dataAfter),
      'created_at': DateTime.now().toIso8601String(),
    });
  }
  
  /// 获取设备ID
  Future<String> _getDeviceId() async {
    // 这里应该从设备信息服务获取设备ID
    // 暂时返回一个固定值
    return 'local_device';
  }
  
  /// 编码JSON
  String _encodeJson(Map<String, dynamic> data) {
    return data.toString();
  }
  
  /// 插入实体
  Future<int> insert(T entity) async {
    final map = toMap(entity);
    
    // 如果不使用自增主键，确保主键存在
    if (!useAutoIncrement && !map.containsKey(primaryKey)) {
      map[primaryKey] = const Uuid().v4();
    }
    
    // 添加时间戳
    final now = DateTime.now().toIso8601String();
    map['created_at'] = now;
    map['updated_at'] = now;
    map['is_dirty'] = 1; // 标记为未同步
    
    final id = await db.insert(tableName, map);
    
    // 添加同步记录
    await addSyncRecord('create', map[primaryKey].toString(), null, map);
    
    return id;
  }
  
  /// 批量插入实体
  Future<List<int>> insertAll(List<T> entities) async {
    final batch = db.batch();
    final ids = <int>[];
    
    for (final entity in entities) {
      final map = toMap(entity);
      
      // 如果不使用自增主键，确保主键存在
      if (!useAutoIncrement && !map.containsKey(primaryKey)) {
        map[primaryKey] = const Uuid().v4();
      }
      
      // 添加时间戳
      final now = DateTime.now().toIso8601String();
      map['created_at'] = now;
      map['updated_at'] = now;
      map['is_dirty'] = 1; // 标记为未同步
      
      batch.insert(tableName, map);
      
      // 添加同步记录
      await addSyncRecord('create', map[primaryKey].toString(), null, map);
    }
    
    final results = await batch.commit();
    for (final result in results) {
      if (result is int) {
        ids.add(result);
      }
    }
    
    return ids;
  }
  
  /// 更新实体
  Future<int> update(T entity) async {
    final map = toMap(entity);
    final id = map[primaryKey];
    
    if (id == null) {
      throw Exception('Primary key is null');
    }
    
    // 获取更新前的数据
    final oldData = await findById(id);
    final oldMap = oldData != null ? toMap(oldData) : null;
    
    // 更新时间戳
    map['updated_at'] = DateTime.now().toIso8601String();
    map['is_dirty'] = 1; // 标记为未同步
    
    final count = await db.update(
      tableName,
      map,
      where: '$primaryKey = ?',
      whereArgs: [id],
    );
    
    // 添加同步记录
    await addSyncRecord('update', id.toString(), oldMap, map);
    
    return count;
  }
  
  /// 删除实体
  Future<int> delete(dynamic id) async {
    // 获取删除前的数据
    final oldData = await findById(id);
    final oldMap = oldData != null ? toMap(oldData) : null;
    
    if (oldMap == null) {
      return 0; // 数据不存在
    }
    
    final count = await db.delete(
      tableName,
      where: '$primaryKey = ?',
      whereArgs: [id],
    );
    
    // 添加同步记录
    await addSyncRecord('delete', id.toString(), oldMap, oldMap);
    
    return count;
  }
  
  /// 根据ID查找实体
  Future<T?> findById(dynamic id) async {
    final maps = await db.query(
      tableName,
      where: '$primaryKey = ?',
      whereArgs: [id],
    );
    
    if (maps.isEmpty) {
      return null;
    }
    
    return fromMap(maps.first);
  }
  
  /// 查找所有实体
  Future<List<T>> findAll({
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    final maps = await db.query(
      tableName,
      orderBy: orderBy,
      limit: limit,
      offset: offset,
    );
    
    return maps.map((map) => fromMap(map)).toList();
  }
  
  /// 根据条件查找实体
  Future<List<T>> findWhere({
    String? where,
    List<Object?>? whereArgs,
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    final maps = await db.query(
      tableName,
      where: where,
      whereArgs: whereArgs,
      orderBy: orderBy,
      limit: limit,
      offset: offset,
    );
    
    return maps.map((map) => fromMap(map)).toList();
  }
  
  /// 计数
  Future<int> count({
    String? where,
    List<Object?>? whereArgs,
  }) async {
    final result = await db.query(
      tableName,
      columns: ['COUNT(*) as count'],
      where: where,
      whereArgs: whereArgs,
    );
    
    return Sqflite.firstIntValue(result) ?? 0;
  }
  
  /// 标记为已同步
  Future<void> markAsSynced(dynamic id, String syncedAt) async {
    await db.update(
      tableName,
      {
        'synced_at': syncedAt,
        'is_dirty': 0,
      },
      where: '$primaryKey = ?',
      whereArgs: [id],
    );
  }
  
  /// 获取未同步的实体
  Future<List<T>> findDirty() async {
    final maps = await db.query(
      tableName,
      where: 'is_dirty = ?',
      whereArgs: [1],
    );
    
    return maps.map((map) => fromMap(map)).toList();
  }
}
