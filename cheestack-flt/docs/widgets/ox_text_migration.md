# Text 组件迁移指南

## 概述

Text 组件已经重构，现在完全基于 Material 3 设计规范和主题系统。这个迁移指南将帮助您从旧版本迁移到新版本。

## 主要变化

### 1. 新增样式系统

新版本引入了基于 Material 3 的样式枚举：

```dart
enum OxTextStyle {
  // 显示文本
  displayLarge, displayMedium, displaySmall,
  
  // 标题文本
  headlineLarge, headlineMedium, headlineSmall,
  
  // 标题
  titleLarge, titleMedium, titleSmall,
  
  // 正文
  bodyLarge, bodyMedium, bodySmall,
  
  // 标签
  labelLarge, labelMedium, labelSmall,
}
```

### 2. 主题集成

- 组件现在完全使用 `Theme.of(context)` 获取样式
- 自动适配浅色/深色主题
- 支持响应式字体大小

### 3. 新增属性

- `style`: 预定义的文本样式类型
- `decoration`: 文本装饰（下划线、删除线等）
- `letterSpacing`: 字母间距
- `height`: 行高

## 迁移步骤

### 步骤 1: 基本文本迁移

**旧版本:**
```dart
Text('Hello World')
```

**新版本:**
```dart
Text('Hello World')  // 无需更改，默认使用 bodyMedium 样式
```

### 步骤 2: 字体大小迁移

**旧版本:**
```dart
Text(
  'Title Text',
  fontSize: FontSize.title,
)
```

**新版本:**
```dart
// 推荐方式：使用预定义样式
Text(
  'Title Text',
  style: OxTextStyle.titleLarge,
)

// 或者继续使用自定义字体大小
Text(
  'Title Text',
  fontSize: 20.0,  // 直接使用数值
)
```

### 步骤 3: 颜色迁移

**旧版本:**
```dart
Text(
  'Colored Text',
  color: Colors.red,
)
```

**新版本:**
```dart
Text(
  'Colored Text',
  color: Colors.red,  // 无需更改
)
```

### 步骤 4: 复杂样式迁移

**旧版本:**
```dart
Text(
  'Complex Text',
  fontSize: FontSize.body,
  color: Colors.blue,
  
  textAlign: TextAlign.center,
  maxLines: 2,
  overflow: TextOverflow.ellipsis,
)
```

**新版本:**
```dart
Text(
  'Complex Text',
  style: OxTextStyle.bodyMedium,  // 使用预定义样式
  color: Colors.blue,
  
  textAlign: TextAlign.center,
  maxLines: 2,
  overflow: TextOverflow.ellipsis,
  // 新增属性
  decoration: TextDecoration.underline,
  letterSpacing: 1.2,
  height: 1.5,
)
```

## 样式映射表

| 旧版本 FontSize | 推荐的新版本 OxTextStyle |
|----------------|------------------------|
| FontSize.body | OxTextStyle.bodyMedium |
| FontSize.title | OxTextStyle.titleMedium |
| FontSize.subtitle | OxTextStyle.titleSmall |
| FontSize.caption | OxTextStyle.bodySmall |
| FontSize.overline | OxTextStyle.labelSmall |

## 最佳实践

### 1. 优先使用预定义样式

```dart
// ✅ 推荐
Text('标题', style: OxTextStyle.titleLarge)

// ❌ 不推荐（除非有特殊需求）
Text('标题', fontSize: 22.0, fontWeight: FontWeight.bold)
```

### 2. 利用主题系统

```dart
// ✅ 组件会自动适配主题
Text('内容', style: OxTextStyle.bodyMedium)

// ❌ 硬编码颜色会破坏主题一致性
Text('内容', color: Color(0xFF000000))
```

### 3. 响应式设计

```dart
// ✅ 使用预定义样式，自动响应式
Text('标题', style: OxTextStyle.headlineLarge)

// ❌ 固定字体大小，不响应式
Text('标题', fontSize: 32.0)
```

## 常见问题

### Q: 如何保持与旧版本完全相同的外观？

A: 如果需要保持完全相同的外观，可以继续使用 `fontSize`、`color`、`fontWeight` 等属性，但建议逐步迁移到新的样式系统。

### Q: 新版本是否向后兼容？

A: 是的，所有旧版本的属性都保留了，您可以逐步迁移。

### Q: 如何在深色主题下测试？

A: 组件会自动适配主题，您只需要在应用中切换主题即可测试。

### Q: 预定义样式不满足需求怎么办？

A: 您可以使用预定义样式作为基础，然后通过 `fontSize`、`color`、`fontWeight` 等属性进行微调。

## 迁移检查清单

- [ ] 将 `FontSize.xxx` 替换为对应的 `OxTextStyle.xxx`
- [ ] 测试浅色和深色主题下的显示效果
- [ ] 验证响应式行为（不同屏幕尺寸）
- [ ] 检查文本对比度和可读性
- [ ] 更新相关文档和注释

## 获取帮助

如果在迁移过程中遇到问题，请：

1. 查看 `text_example.dart` 中的使用示例
2. 运行测试确保功能正常：`flutter test test/widgets/ox_text_test.dart`
3. 参考 Material 3 设计规范文档
