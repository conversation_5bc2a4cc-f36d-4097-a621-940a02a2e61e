# 模块化开发文档模板规范

## 📋 文档目录

- [🎯 模板用途](#-模板用途)
- [⚠️ AI生成要求](#️-ai生成要求)
- [📋 模板结构](#-模板结构)
- [📊 模块质量标准](#-模块质量标准)
- [🔧 技术栈规范](#-技术栈规范)
- [🏗️ 后端开发设计](#️-后端开发设计)
- [📱 前端开发设计](#-前端开发设计)
- [🔗 API接口设计](#-api接口设计)
- [🚀 开发实施](#-开发实施)
- [✅ 验收标准](#-验收标准)
- [📝 质量检查清单](#-质量检查清单)
- [📚 附录](#-附录)
- [🔍 内容生成要求](#-内容生成要求)
- [⚠️ 注意事项](#️-注意事项)

## 🎯 模板用途
当AI生成模块化开发文档时，必须按照此模板结构，为每个功能模块提供架构设计和开发指导，确保开发团队能够基于PRD中的功能需求进行模块化开发。模板专注于架构设计和必要说明，具体开发规范严格遵循项目的FastAPI和Flutter规则文档。

## 🔧 技术栈规范

### 强制技术栈要求
本项目采用以下技术栈，**严格禁止**使用其他技术替代：

#### 后端技术栈（严格遵循 fastapi-rule.md）
- **Web框架**: FastAPI（异步高性能框架）
- **ORM**: Tortoise ORM（异步ORM，**禁止使用SQLAlchemy**）
- **数据库**: PostgreSQL（主数据库）+ Redis（缓存）
- **数据验证**: Pydantic v2（数据模型和验证）
- **数据库迁移**: Aerich（Tortoise ORM的迁移工具）
- **异步HTTP客户端**: httpx
- **密码加密**: passlib + bcrypt
- **JWT认证**: PyJWT
- **配置管理**: pydantic-settings
- **测试框架**: pytest + pytest-asyncio
- **项目架构**: CheeStack架构（apps/[module_name]目录结构）

#### 前端技术栈（严格遵循 flutter-rule.md）
- **移动端框架**: Flutter
- **状态管理**: GetX（**禁止使用Provider、Bloc等其他状态管理**）
- **架构模式**: MVVM + Repository模式
- **HTTP客户端**: Dio + 自定义网络层
- **依赖注入**: GetX依赖注入
- **路由管理**: GetX路由管理
- **屏幕适配**: flutter_screenutil（**强制使用**）
- **测试**: flutter test（**禁止使用flutter run进行测试**）

#### 开发规范强制要求
- **后端**: 严格遵循 `.augment/rules/fastapi-rule.md` 中的所有规范
- **前端**: 严格遵循 `.augment/rules/flutter-rule.md` 中的所有规范
- **语言**: 所有代码和文档使用中文
- **架构**: 高内聚低耦合的模块化设计
- **测试**: 代码覆盖率 > 80%

## ⚠️ AI生成要求
- **PRD依赖性**：开发文档必须完全基于PRD文档的功能需求，不得添加PRD中未定义的功能
- **用户故事对应性**：每个开发模块必须与用户故事中的具体场景完全对应
- **内容溯源性**：所有技术设计和实现方案都必须能够追溯到PRD或用户故事的具体需求
- **数据一致性**：数据结构、接口定义、业务流程必须与PRD和用户故事保持完全一致
- **技术栈一致性**：严格遵循CheeStack项目架构，后端使用FastAPI+Tortoise ORM，前端使用Flutter+GetX
- **架构清晰性**：重点提供清晰的架构图和数据流设计
- **开发指导性**：提供必要的技术选型和实现思路说明，但避免过度详细的代码实现

## 📋 [模块名称]-development.md 模板结构

```markdown
# [模块名称]开发文档

## 1. 文档信息

| 版本 | 日期       | 作者      | 备注 |
| ---- | ---------- | --------- | ---- |
| 1.0  | yyyy-mm-dd | your_name | 初版 |

**关联PRD**: [链接到对应的PRD功能模块章节]
**关联用户故事**: [链接到对应的用户故事章节]
**优先级**: Must/Should/Could/Won't（来源于PRD）
**预估工期**: [X]人天（前端[X]人天 + 后端[X]人天）

### 📋 文档依赖关系
- **主要依赖**: PRD文档 → 本开发文档
- **次要依赖**: 用户故事文档 → 本开发文档
- **数据来源**: 所有功能需求、业务规则、数据结构均来源于PRD
- **场景来源**: 所有用户场景、交互流程均来源于用户故事
- **一致性要求**: 本文档不得包含PRD和用户故事中未定义的内容

---

## 1.5. 模块质量标准

### 📊 质量评估维度

#### 完整性标准 (Completeness)
- [ ] **结构完整**：按照模板要求包含所有必要章节
- [ ] **内容完整**：每个章节都有具体内容，无空白章节
- [ ] **信息完整**：包含模块实施所需的所有关键信息
- [ ] **覆盖完整**：覆盖需求、设计、实现、测试全生命周期

**量化指标**：
- 必需章节覆盖率 ≥ 100%
- 内容填充率 ≥ 95%
- 关键信息覆盖率 ≥ 90%

#### 准确性标准 (Accuracy)
- [ ] **技术准确**：技术方案和架构设计准确可行
- [ ] **业务准确**：业务逻辑和流程描述准确
- [ ] **数据准确**：数据结构和接口定义准确
- [ ] **时间准确**：时间估算和计划安排合理

**量化指标**：
- 技术方案可行性 ≥ 95%
- 业务逻辑正确性 ≥ 98%
- 数据结构准确性 ≥ 99%
- 时间估算合理性 ≥ 85%

#### 一致性标准 (Consistency)
- [ ] **术语一致**：全文使用统一的术语和概念
- [ ] **格式一致**：遵循统一的格式和样式规范
- [ ] **数据一致**：数据结构在各文档中保持一致
- [ ] **逻辑一致**：业务逻辑在各文档中保持一致

**量化指标**：
- 术语使用一致性 ≥ 98%
- 格式规范一致性 ≥ 100%
- 数据结构一致性 ≥ 99%
- 逻辑描述一致性 ≥ 95%

#### 可执行性标准 (Executability)
- [ ] **需求可实现**：需求描述具体，可以转化为具体功能
- [ ] **设计可实施**：技术设计详细，开发人员可以直接实施
- [ ] **任务可执行**：任务分解到位，可以直接分配和执行
- [ ] **测试可验证**：验收标准明确，可以直接转化为测试用例

**量化指标**：
- 需求可实现性 ≥ 90%
- 技术方案可实施性 ≥ 95%
- 任务可执行性 ≥ 98%
- 验收标准可验证性 ≥ 92%

---

## 2. 模块概览

### 2.1 功能概述（来源：PRD文档）
- **模块定义**：[从PRD第X章节复制的功能定义，保持原文不变]
- **核心价值**：[从PRD第X章节复制的核心价值，保持原文不变]
- **业务场景**：[从PRD第X章节复制的使用场景，保持原文不变]
- **前置条件**：[从PRD第X章节复制的前置条件，保持原文不变]
- **验收标准**：[从PRD第X章节复制的验收标准，保持原文不变]

### 2.1.1 用户故事映射（来源：用户故事文档）
- **主要用户故事**：[从用户故事文档中复制相关的用户故事]
- **用户角色**：[从用户故事中提取的用户角色]
- **使用场景**：[从用户故事中提取的具体使用场景]
- **用户价值**：[从用户故事中提取的用户价值]

### 2.1.2 需求追溯性检查
- [ ] **PRD对应性**：确认所有功能都来源于PRD文档
- [ ] **用户故事对应性**：确认所有场景都来源于用户故事
- [ ] **无额外功能**：确认没有添加PRD和用户故事之外的功能
- [ ] **数据一致性**：确认数据结构与PRD保持一致

### 2.2 技术架构
```mermaid
graph TB
    subgraph "前端层 (Flutter/Web)"
        A[用户界面]
        B[状态管理]
        C[业务逻辑]
        D[数据缓存]
    end

    subgraph "API层"
        E[API网关]
        F[认证中间件]
        G[参数验证]
    end

    subgraph "后端层 (FastAPI)"
        H[路由控制器]
        I[业务服务]
        J[数据访问层]
    end

    subgraph "数据层"
        K[主数据库]
        L[缓存数据库]
    end

    A --> E
    E --> H
    H --> I
    I --> J
    J --> K
    J --> L
```

### 2.3 数据流设计
```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as 前端界面
    participant API as API服务
    participant Service as 业务服务
    participant DB as 数据库

    User->>UI: 用户操作
    UI->>API: HTTP请求
    API->>Service: 调用业务逻辑
    Service->>DB: 数据操作
    DB-->>Service: 返回结果
    Service-->>API: 业务结果
    API-->>UI: JSON响应
    UI-->>User: 界面更新
```

---

## 3. 后端开发设计

### 3.1 技术选型
- **Web框架**：FastAPI（严格遵循项目FastAPI规则）
- **数据库**：PostgreSQL
- **缓存**：Redis
- **ORM**：Tortoise ORM（异步ORM，**禁止使用SQLAlchemy**）
- **数据验证**：Pydantic v2
- **项目架构**：严格遵循CheeStack项目目录结构
- **异步HTTP客户端**：httpx
- **密码加密**：passlib + bcrypt
- **JWT认证**：PyJWT
- **配置管理**：pydantic-settings
- **测试框架**：pytest + pytest-asyncio

### 3.2 核心组件设计

#### 3.2.1 CheeStack架构组件设计
按照CheeStack项目架构规范，每个模块必须包含以下文件：

- `__init__.py`: 模块初始化文件
- `apis.py`: API路由定义，使用APIRouter
- `models.py`: Tortoise ORM数据模型定义
- `schema.py`: Pydantic数据验证和序列化模型
- `deps.py`: 依赖注入函数定义
- `curd.py`: 数据库CRUD操作函数
- `config.py`: 模块特定配置（可选）
- `consts.py`: 模块常量定义（可选）

```python
# apps/[module_name]/models.py - Tortoise ORM模型
from tortoise.models import Model
from tortoise import fields
from core.models import TimeStampModelMixin

class [Entity]Model(TimeStampModelMixin):
    """[实体]数据模型"""
    id = fields.CharField(
        max_length=100,
        pk=True,
        description="[实体]ID",
        unique=True,
        default=shortuuid.uuid,
    )
    name = fields.CharField(max_length=100, description="[实体]名称")
    description = fields.TextField(null=True, description="[实体]描述")
    status = fields.CharField(max_length=20, default="active", description="状态")

    class PydanticMeta:
        exclude = ["is_deleted"]

    class Meta:
        table = "[entities]"
        table_description = "[实体]表"
        ordering = ["-created_at", "id"]

# apps/[module_name]/schema.py - Pydantic数据验证模型
from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime

class Create[Entity]Request(BaseModel):
    """创建[实体]请求模型"""
    name: str = Field(..., description="[实体]名称")
    description: Optional[str] = Field(None, description="[实体]描述")

class [Entity]Response(BaseModel):
    """[实体]响应模型"""
    id: str
    name: str
    description: Optional[str] = None
    status: str
    created_at: datetime
    updated_at: datetime
```

#### 3.2.2 CRUD操作设计
```python
# apps/[module_name]/curd.py - 数据库CRUD操作
from typing import List, Optional
from tortoise.expressions import Q

async def create_[entity](data: Create[Entity]Request) -> [Entity]Model:
    """创建[实体]"""
    return await [Entity]Model.create(**data.dict())

async def get_[entity]_list(skip: int = 0, limit: int = 100) -> List[[Entity]Model]:
    """获取[实体]列表"""
    return await [Entity]Model.all().offset(skip).limit(limit)

async def get_[entity]_by_id(entity_id: str) -> Optional[[Entity]Model]:
    """根据ID获取[实体]"""
    return await [Entity]Model.get_or_none(id=entity_id)

async def update_[entity](entity_id: str, data: dict) -> [Entity]Model:
    """更新[实体]信息"""
    await [Entity]Model.filter(id=entity_id).update(**data)
    return await [Entity]Model.get(id=entity_id)

async def delete_[entity](entity_id: str) -> bool:
    """删除[实体]"""
    deleted_count = await [Entity]Model.filter(id=entity_id).delete()
    return deleted_count > 0
```

#### 3.2.3 API路由设计
```python
# apps/[module_name]/apis.py - API路由定义
from fastapi import APIRouter, Depends, HTTPException
from core.responses import success_response, error_response
from core.schema import resmod

router = APIRouter(prefix="/api/[module-name]", tags=["[模块名称]"])

@router.post(
    "/",
    summary="创建[实体]",
    description="创建新的[实体]记录",
    response_model=resmod([Entity]Response),
)
async def create_[entity](data: Create[Entity]Request):
    """创建[实体]"""
    try:
        entity = await create_[entity](data)
        return success_response(data=entity)
    except Exception as e:
        return error_response(message=str(e))

@router.get(
    "/",
    summary="获取[实体]列表",
    description="分页获取[实体]列表",
    response_model=resmod(List[[Entity]Response]),
)
async def get_[entity]_list(skip: int = 0, limit: int = 100):
    """获取[实体]列表"""
    entities = await get_[entity]_list(skip, limit)
    return success_response(data=entities)
```

#### 3.2.4 依赖注入设计
```python
# apps/[module_name]/deps.py - 依赖注入函数
from fastapi import Depends, HTTPException
from core.deps import get_current_user
from apps.auth.models import User

async def get_[entity]_owner(
    entity_id: str,
    current_user: User = Depends(get_current_user)
) -> User:
    """验证当前用户是否为[实体]所有者"""
    entity = await [Entity]Model.get_or_none(id=entity_id)

    if not entity:
        raise HTTPException(status_code=404, detail="[实体]不存在")

    if entity.created_by != current_user.id:
        raise HTTPException(status_code=403, detail="无权限操作此[实体]")

    return current_user
```

### 3.3 数据库设计

#### 3.3.1 表结构设计
```sql
-- [entities]表
CREATE TABLE [entities] (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(36) NOT NULL,
    is_deleted BOOLEAN DEFAULT FALSE
);

-- 创建索引
CREATE INDEX idx_[entities]_name ON [entities] (name);
CREATE INDEX idx_[entities]_status ON [entities] (status);
CREATE INDEX idx_[entities]_created_at ON [entities] (created_at);
CREATE INDEX idx_[entities]_created_by ON [entities] (created_by);
```

#### 3.3.2 数据库迁移配置
```python
# aerich.ini
[aerich]
tortoise_orm = core.postgresql.TORTOISE_ORM
location = ./migrations
src_folder = ./

# 使用Aerich进行迁移
# 初始化: aerich init -t core.postgresql.TORTOISE_ORM
# 创建迁移: aerich migrate --name add_[entity]
# 应用迁移: aerich upgrade
```

#### 3.3.3 Tortoise ORM配置
```python
# core/postgresql.py 中的模型注册
TORTOISE_ORM = {
    "connections": {
        "default": {
            "engine": "tortoise.backends.asyncpg",
            "credentials": {
                "host": settings.DB_HOST,
                "port": settings.DB_PORT,
                "user": settings.DB_USER,
                "password": settings.DB_PASSWORD,
                "database": settings.DB_NAME,
                "minsize": 1,
                "maxsize": 10,
            }
        }
    },
    "apps": {
        "models": {
            "models": [
                "apps.[module_name].models",  # 注册模块模型
                "aerich.models"
            ],
            "default_connection": "default",
        },
    },
}
```

### 3.4 缓存策略
```python
# 缓存实现示例
from core.redis import redis_client
import json

async def get_cached_[entity]_list(skip: int = 0, limit: int = 100):
    """获取缓存的[实体]列表"""
    cache_key = f"[module]:[entity]:list:{skip}:{limit}"
    cached_data = await redis_client.get(cache_key)

    if cached_data:
        return json.loads(cached_data)

    # 从数据库获取数据
    entities = await get_[entity]_list(skip, limit)

    # 序列化并缓存数据
    serialized_data = json.dumps([entity.dict() for entity in entities])
    await redis_client.setex(cache_key, 300, serialized_data)  # 5分钟过期

    return entities
```

### 3.5 异常处理
```python
# 自定义异常处理
from fastapi import Request
from fastapi.responses import JSONResponse
from core.exceptions import Sexception

@router.exception_handler(Sexception)
async def custom_exception_handler(request: Request, exc: Sexception):
    return JSONResponse(
        status_code=exc.status_code,
        content={"code": exc.status_code, "msg": exc.detail, "data": []}
    )
```

---

## 4. 前端开发设计

### 4.1 技术选型
- **移动端框架**：Flutter（严格遵循项目Flutter规则）
- **状态管理**：GetX（**禁止使用Provider、Bloc等其他状态管理**）
- **HTTP客户端**：Dio + 自定义网络层
- **架构模式**：MVVM + Repository模式
- **依赖注入**：GetX依赖注入
- **路由管理**：GetX路由管理
- **屏幕适配**：flutter_screenutil（**强制使用**）
- **测试**：flutter test（**禁止使用flutter run进行测试**）

### 4.2 项目结构规范

#### 4.2.1 标准模块化目录结构
```
lib/features/[module_name]/
├── models/                  # 数据模型
│   ├── [entity]_model.dart
│   └── index.dart
├── services/               # 网络请求服务
│   ├── [entity]_api.dart
│   └── index.dart
├── controllers/            # 状态管理控制器
│   ├── [entity]_controller.dart
│   └── index.dart
├── views/                  # 表现层
│   ├── [entity]_page.dart  # 页面，需配置路由
│   ├── [entity]_list_page.dart
│   └── widgets/            # 功能特定组件
│       ├── [entity]_form.dart
│       ├── [entity]_list_item.dart
│       └── index.dart
└── index.dart              # 采用export方式导出模块文件
```

### 4.3 架构设计

#### 4.3.1 Flutter MVVM架构设计
按照Flutter规则，采用GetX + MVVM + Repository模式：

```dart
// lib/features/[module_name]/models/[entity]_model.dart
class [Entity]Model {
  final String id;
  final String name;
  final String? description;
  final String status;
  final DateTime createdAt;
  final DateTime updatedAt;

  const [Entity]Model({
    required this.id,
    required this.name,
    this.description,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  factory [Entity]Model.fromJson(Map<String, dynamic> json) {
    return [Entity]Model(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      status: json['status'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

// lib/features/[module_name]/services/[entity]_api.dart
class [Entity]Api extends GetxService {
  final Dio _dio = Get.find<Dio>();

  Future<ApiResult<List<[Entity]Model>>> get[Entity]List({
    int skip = 0,
    int limit = 20,
  }) async {
    try {
      final response = await _dio.get(
        '/api/[module-name]',
        queryParameters: {'skip': skip, 'limit': limit},
      );

      if (response.data['success'] == true) {
        final List<dynamic> dataList = response.data['data'] as List<dynamic>;
        final entities = dataList
            .map((json) => [Entity]Model.fromJson(json as Map<String, dynamic>))
            .toList();
        return ApiResult.success(entities);
      } else {
        return ApiResult.error(response.data['message'] as String);
      }
    } catch (e) {
      return ApiResult.error('网络请求失败: ${e.toString()}');
    }
  }

  Future<ApiResult<[Entity]Model>> create[Entity](
    Create[Entity]Request request,
  ) async {
    try {
      final response = await _dio.post(
        '/api/[module-name]',
        data: request.toJson(),
      );

      if (response.data['success'] == true) {
        final entity = [Entity]Model.fromJson(
          response.data['data'] as Map<String, dynamic>,
        );
        return ApiResult.success(entity);
      } else {
        return ApiResult.error(response.data['message'] as String);
      }
    } catch (e) {
      return ApiResult.error('创建失败: ${e.toString()}');
    }
  }
}

// lib/features/[module_name]/controllers/[entity]_controller.dart
class [Entity]Controller extends GetxController {
  final [Entity]Api _api = Get.find<[Entity]Api>();

  // 使用GetBuilder更新状态，禁用obs
  List<[Entity]Model> entities = [];
  bool isLoading = false;
  String error = '';

  @override
  void onInit() {
    super.onInit();
    load[Entity]List();
  }

  Future<void> load[Entity]List() async {
    isLoading = true;
    error = '';
    update(); // 使用update()更新状态

    final result = await _api.get[Entity]List();

    if (result.isSuccess) {
      entities = result.data!;
    } else {
      error = result.message;
    }

    isLoading = false;
    update(); // 使用update()更新状态
  }

  Future<void> create[Entity](Create[Entity]Request request) async {
    final result = await _api.create[Entity](request);

    if (result.isSuccess) {
      entities.add(result.data!);
      update(); // 使用update()更新状态
      Get.snackbar('成功', '[实体]创建成功');
      Get.back(); // 返回上一页
    } else {
      Get.snackbar('错误', result.message);
    }
  }

  void navigateTo[Entity]Detail(String entityId) {
    Get.toNamed('/[module-name]/detail', arguments: entityId);
  }

  void navigateToCreate[Entity]() {
    Get.toNamed('/[module-name]/create');
  }
}
```

### 4.4 界面设计

#### 4.4.1 页面结构
- **列表页面**：显示[实体]列表，支持搜索、筛选、分页
- **详情页面**：显示[实体]详细信息
- **创建/编辑页面**：[实体]的创建和编辑表单

#### 4.4.2 Flutter UI组件设计
```dart
// lib/features/[module_name]/views/[entity]_list_page.dart
class [Entity]ListPage extends StatelessWidget {
  const [Entity]ListPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('[实体]列表'),
        backgroundColor: Theme.of(Get.context!).colorScheme.primary,
        foregroundColor: Theme.of(Get.context!).colorScheme.onPrimary,
      ),
      body: GetBuilder<[Entity]Controller>(
        builder: (controller) {
          if (controller.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (controller.error.isNotEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    controller.error,
                    style: TextStyle(
                      color: Theme.of(Get.context!).colorScheme.error,
                    ),
                  ),
                  SizedBox(height: 16.h),
                  ElevatedButton(
                    onPressed: controller.load[Entity]List,
                    child: const Text('重试'),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            padding: EdgeInsets.all(16.w),
            itemCount: controller.entities.length,
            itemBuilder: (context, index) {
              final entity = controller.entities[index];
              return [Entity]ListItem(
                entity: entity,
                onTap: () => controller.navigateTo[Entity]Detail(entity.id),
              );
            },
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => Get.find<[Entity]Controller>().navigateToCreate[Entity](),
        backgroundColor: Theme.of(Get.context!).colorScheme.primary,
        child: Icon(
          Icons.add,
          color: Theme.of(Get.context!).colorScheme.onPrimary,
        ),
      ),
    );
  }
}

// lib/features/[module_name]/views/widgets/[entity]_list_item.dart
class [Entity]ListItem extends StatelessWidget {
  final [Entity]Model entity;
  final VoidCallback? onTap;

  const [Entity]ListItem({
    super.key,
    required this.entity,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(Get.context!).colorScheme;

    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      child: ListTile(
        contentPadding: EdgeInsets.symmetric(
          horizontal: 16.w,
          vertical: 8.h,
        ),
        title: Text(
          entity.name,
          style: TextStyle(
            fontSize: 16.sp,
            
            color: colorScheme.onSurface,
          ),
        ),
        subtitle: entity.description != null
            ? Text(
                entity.description!,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: colorScheme.onSurfaceVariant,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              )
            : null,
        trailing: Container(
          padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
          decoration: BoxDecoration(
            color: entity.status == 'active'
                ? colorScheme.primaryContainer
                : colorScheme.surfaceVariant,
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Text(
            entity.status,
            style: TextStyle(
              fontSize: 12.sp,
              color: entity.status == 'active'
                  ? colorScheme.onPrimaryContainer
                  : colorScheme.onSurfaceVariant,
            ),
          ),
        ),
        onTap: onTap,
      ),
    );
  }
}

// lib/features/[module_name]/views/widgets/[entity]_form.dart
class [Entity]Form extends StatefulWidget {
  final [Entity]Model? entity;
  final Function(Create[Entity]Request) onSubmit;

  const [Entity]Form({
    super.key,
    this.entity,
    required this.onSubmit,
  });

  @override
  State<[Entity]Form> createState() => _[Entity]FormState();
}

class _[Entity]FormState extends State<[Entity]Form> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  @override
  void initState() {
    super.initState();
    if (widget.entity != null) {
      _nameController.text = widget.entity!.name;
      _descriptionController.text = widget.entity!.description ?? '';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(Get.context!).colorScheme;

    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          TextFormField(
            controller: _nameController,
            decoration: InputDecoration(
              labelText: '[实体]名称',
              labelStyle: TextStyle(color: colorScheme.onSurfaceVariant),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: BorderSide(color: colorScheme.primary),
              ),
            ),
            style: TextStyle(
              fontSize: 16.sp,
              color: colorScheme.onSurface,
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return '请输入[实体]名称';
              }
              return null;
            },
          ),
          SizedBox(height: 16.h),
          TextFormField(
            controller: _descriptionController,
            decoration: InputDecoration(
              labelText: '[实体]描述',
              labelStyle: TextStyle(color: colorScheme.onSurfaceVariant),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: BorderSide(color: colorScheme.primary),
              ),
            ),
            style: TextStyle(
              fontSize: 16.sp,
              color: colorScheme.onSurface,
            ),
            maxLines: 3,
          ),
          SizedBox(height: 24.h),
          ElevatedButton(
            onPressed: _submitForm,
            style: ElevatedButton.styleFrom(
              backgroundColor: colorScheme.primary,
              foregroundColor: colorScheme.onPrimary,
              padding: EdgeInsets.symmetric(vertical: 16.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            child: Text(
              '提交',
              style: TextStyle(
                fontSize: 16.sp,
                
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      final request = Create[Entity]Request(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
      );
      widget.onSubmit(request);
    }
  }
}
```

### 4.5 路由配置
```dart
// lib/routes/app_routes.dart
class AppRoutes {
  static const String [module_name]List = '/[module-name]';
  static const String [module_name]Detail = '/[module-name]/detail';
  static const String [module_name]Create = '/[module-name]/create';
  static const String [module_name]Edit = '/[module-name]/edit';
}

// 路由页面配置
GetPage(
  name: AppRoutes.[module_name]List,
  page: () => const [Entity]ListPage(),
  binding: [Entity]Binding(),
),
GetPage(
  name: AppRoutes.[module_name]Detail,
  page: () => const [Entity]DetailPage(),
  binding: [Entity]Binding(),
),
GetPage(
  name: AppRoutes.[module_name]Create,
  page: () => const [Entity]CreatePage(),
  binding: [Entity]Binding(),
),
```

### 4.6 依赖注入配置
```dart
// lib/features/[module_name]/bindings/[entity]_binding.dart
class [Entity]Binding extends Bindings {
  @override
  void dependencies() {
    // 注册API服务
    Get.lazyPut<[Entity]Api>(() => [Entity]Api());

    // 注册控制器
    Get.lazyPut<[Entity]Controller>(() => [Entity]Controller());
  }
}
```

---

## 5. API接口设计

### 5.1 接口规范
基于RESTful设计原则，提供以下接口：

| 方法   | 路径                    | 描述           |
| ------ | ----------------------- | -------------- |
| POST   | /api/[module-name]      | 创建[实体]     |
| GET    | /api/[module-name]      | 获取[实体]列表 |
| GET    | /api/[module-name]/{id} | 获取[实体]详情 |
| PUT    | /api/[module-name]/{id} | 更新[实体]     |
| DELETE | /api/[module-name]/{id} | 删除[实体]     |

### 5.2 数据格式
```json
// 请求格式
{
  "name": "string",
  "description": "string"
}

// 响应格式
{
  "success": true,
  "message": "操作成功",
  "data": {
    "id": "string",
    "name": "string",
    "description": "string",
    "status": "active",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  }
}
```

---

## 6. 开发实施

### 6.1 开发顺序
1. **数据库设计**：创建表结构和索引
2. **后端开发**：实现API接口和业务逻辑
3. **前端开发**：实现用户界面和交互
4. **联调测试**：前后端联调和功能测试
5. **部署上线**：部署到测试和生产环境

### 6.2 技术要求

#### 6.2.1 后端开发要求（严格遵循 fastapi-rule.md）
- **架构规范**：严格遵循CheeStack项目架构和FastAPI开发规则
- **ORM使用**：**必须使用Tortoise ORM**，禁止使用SQLAlchemy
- **目录结构**：严格遵循apps/[module_name]目录结构
- **数据验证**：使用Pydantic v2进行数据验证和序列化
- **响应格式**：实现统一的响应格式和错误处理
- **异步编程**：所有I/O操作必须使用async/await
- **依赖注入**：使用FastAPI的依赖注入系统
- **安全性**：实现JWT认证、密码加密、输入验证
- **缓存策略**：合理使用Redis缓存提高性能
- **日志记录**：使用sutils.log进行结构化日志记录

#### 6.2.2 前端开发要求（严格遵循 flutter-rule.md）
- **状态管理**：**必须使用GetX**，禁止使用Provider、Bloc等
- **架构模式**：严格遵循MVVM + Repository架构模式
- **状态更新**：使用GetBuilder配合update()更新，**禁用obs更新状态**
- **屏幕适配**：**强制使用flutter_screenutil**进行屏幕适配
- **主题颜色**：**必须使用**`Theme.of(Get.context!).colorScheme`获取颜色
- **路由管理**：使用GetX路由管理，所有导航通过Get.toNamed
- **依赖注入**：使用GetX依赖注入管理服务和控制器
- **错误处理**：实现统一的错误处理和用户反馈机制
- **性能优化**：使用const构造函数、合理的Widget拆分
- **测试规范**：使用flutter test，**禁止使用flutter run进行测试**

#### 6.2.3 代码质量要求
- **代码覆盖率**：单元测试覆盖率 > 80%
- **代码规范**：遵循PEP 8（Python）和Dart官方规范
- **类型安全**：所有函数和变量必须声明类型
- **错误处理**：完善的异常处理和用户友好的错误提示
- **文档注释**：关键业务逻辑必须有中文注释
- **性能要求**：API响应时间 < 500ms，页面加载时间 < 3s

### 6.3 测试策略

#### 6.3.1 后端测试
```python
# 单元测试示例
import pytest
from httpx import AsyncClient
from main import app

@pytest.mark.asyncio
async def test_create_[entity]():
    """测试创建[实体]"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.post(
            "/api/[module-name]/",
            json={"name": "测试[实体]", "description": "测试描述"}
        )

    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["data"]["name"] == "测试[实体]"

@pytest.mark.asyncio
async def test_get_[entity]_list():
    """测试获取[实体]列表"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.get("/api/[module-name]/")

    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert isinstance(data["data"], list)
```

#### 6.3.2 前端测试
```dart
// 单元测试示例
// test/features/[module_name]/controllers/[entity]_controller_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';

void main() {
  group('[Entity]Controller 测试', () {
    late [Entity]Controller controller;

    setUp(() {
      // 初始化GetX和依赖
      Get.testMode = true;
      Get.put<[Entity]Api>([Entity]Api());
      controller = [Entity]Controller();
    });

    tearDown(() {
      // 清理GetX状态
      Get.reset();
    });

    test('应该能够加载[实体]列表', () async {
      // Arrange
      expect(controller.entities, isEmpty);
      expect(controller.isLoading, false);

      // Act
      await controller.load[Entity]List();

      // Assert
      expect(controller.isLoading, false);
      expect(controller.error, isEmpty);
    });

    test('应该能够创建新[实体]', () async {
      // Arrange
      final request = Create[Entity]Request(
        name: '测试[实体]',
        description: '测试描述',
      );

      // Act
      await controller.create[Entity](request);

      // Assert
      expect(controller.entities.isNotEmpty, true);
    });
  });
}

// Widget测试示例
// test/features/[module_name]/widgets/[entity]_list_item_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('[Entity]ListItem Widget 测试', () {
    testWidgets('应该正确显示[实体]信息', (WidgetTester tester) async {
      // Arrange
      final entity = [Entity]Model(
        id: '1',
        name: '测试[实体]',
        description: '测试描述',
        status: 'active',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: [Entity]ListItem(entity: entity),
          ),
        ),
      );

      // Assert
      expect(find.text('测试[实体]'), findsOneWidget);
      expect(find.text('测试描述'), findsOneWidget);
      expect(find.text('active'), findsOneWidget);
    });
  });
}
```

#### 6.3.3 性能测试
- **API性能测试**：使用pytest-benchmark测试API响应时间
- **前端性能测试**：使用Flutter的性能分析工具
- **数据库性能测试**：测试查询优化和索引效果
- **并发测试**：测试高并发场景下的系统稳定性

---

## 7. 验收标准

### 7.1 功能验收
基于PRD中的验收标准：
- [ ] **正常流程验收**：[从PRD复制的正常流程验收标准]
- [ ] **异常流程验收**：[从PRD复制的异常流程验收标准]
- [ ] **性能要求验收**：[从PRD复制的性能要求验收标准]

### 7.2 技术验收
- [ ] **代码质量**：通过代码审查，符合项目规范
- [ ] **测试覆盖**：单元测试覆盖率 > 80%
- [ ] **性能指标**：满足响应时间和并发要求
- [ ] **安全性**：通过安全扫描，无高危漏洞

---

## 7.5. 模块质量检查清单

### ✅ 内容完整性检查
- [ ] **无空白占位符**：所有 `[占位符]`、`[具体描述]`、`[详细说明]` 等都已替换为具体内容
- [ ] **章节内容充实**：每个章节都有实质性内容，不是简单的标题或一句话描述
- [ ] **示例完整具体**：所有代码示例、数据示例、配置示例都是完整可用的
- [ ] **图表清晰完整**：所有Mermaid图表都有完整的节点、连接和标注
- [ ] **表格信息完整**：所有表格都有具体的数据，不是空白或示例数据

### ✅ 结构规范性检查
- [ ] **标题层级正确**：使用正确的Markdown标题层级（#、##、###等）
- [ ] **格式统一规范**：代码块、表格、列表格式统一且规范
- [ ] **引用链接有效**：所有内部引用和外部链接都是有效的
- [ ] **文档结构清晰**：章节组织逻辑清晰，便于阅读和查找
- [ ] **版本信息完整**：文档头部的版本、日期、作者信息完整

### ✅ 技术准确性检查
- [ ] **技术栈正确**：使用Flutter + FastAPI + Tortoise ORM技术栈
- [ ] **架构规范遵循**：严格遵循CheeStack项目架构规范
- [ ] **代码语法正确**：所有代码示例语法正确，符合语言规范
- [ ] **最佳实践遵循**：代码和架构设计遵循各技术栈的最佳实践
- [ ] **GetX模式正确**：Flutter代码遵循GetX + MVVM + Repository模式
- [ ] **Tortoise ORM规范**：后端数据模型遵循Tortoise ORM规范
- [ ] **版本信息准确**：技术栈版本号准确且为推荐版本
- [ ] **配置信息可用**：所有配置文件和环境配置都是可用的

### ✅ 模块开发文档专项检查
- [ ] **架构图完整**：系统架构图清晰展示各组件关系
- [ ] **技术选型有理由**：每个技术选择都有明确的理由和对比
- [ ] **模块设计详细**：每个模块都有详细的设计说明和代码示例
- [ ] **接口定义清晰**：模块间接口定义清晰，包含数据格式
- [ ] **性能考虑充分**：包含性能优化策略和监控方案
- [ ] **安全设计完整**：包含完整的安全设计和防护措施

### ✅ API接口检查
- [ ] **接口覆盖完整**：覆盖PRD中所有功能需求的API接口
- [ ] **请求响应完整**：每个接口都有完整的请求参数和响应示例
- [ ] **数据模型一致**：API数据模型与PRD中的数据需求一致
- [ ] **错误处理完整**：包含完整的错误码定义和处理机制
- [ ] **认证授权明确**：API认证和授权机制清晰定义
- [ ] **FastAPI规范**：遵循FastAPI和Pydantic v2的最佳实践
- [ ] **CheeStack架构**：遵循apps/[module_name]目录结构和组件划分
- [ ] **Tortoise ORM**：正确使用Tortoise ORM进行数据库操作
- [ ] **统一响应格式**：使用core.responses中的统一响应格式

### ✅ 数据库设计检查
- [ ] **ER图完整**：数据库ER图清晰展示表关系
- [ ] **表结构详细**：每个表都有详细的字段定义和约束
- [ ] **索引设计合理**：包含合理的索引设计和性能优化
- [ ] **数据一致性**：数据库设计与API接口数据模型一致
- [ ] **Tortoise模型规范**：Tortoise ORM模型定义规范正确
- [ ] **迁移脚本完整**：包含数据库创建和迁移脚本
- [ ] **字段类型正确**：使用正确的Tortoise字段类型和约束

### ✅ 前端设计检查
- [ ] **组件规范详细**：每个UI组件都有详细的设计规范
- [ ] **Flutter规范**：遵循Flutter Material Design设计原则
- [ ] **GetX架构正确**：正确使用GetX进行状态管理和依赖注入
- [ ] **MVVM模式**：遵循Controller + Service + Repository架构模式
- [ ] **响应式设计**：包含不同设备的适配方案
- [ ] **性能优化**：包含前端性能优化考虑
- [ ] **错误处理统一**：统一的错误处理和用户反馈机制

### ✅ 跨文档一致性检查
- [ ] **术语定义统一**：所有文档中的专业术语定义保持一致
- [ ] **概念模型一致**：核心业务概念在各文档中表述一致
- [ ] **命名规范统一**：变量名、函数名、接口名等命名规范统一
- [ ] **状态定义一致**：业务状态、系统状态定义在各文档中一致
- [ ] **API数据模型**：API接口的数据模型与PRD中的数据需求一致
- [ ] **数据库设计**：数据库表结构与API数据模型对应
- [ ] **前端数据模型**：Flutter的数据模型与API接口一致

### ✅ 可执行性检查
- [ ] **技术方案可行**：所有技术方案都经过可行性验证
- [ ] **代码示例可运行**：所有代码示例都是完整可运行的
- [ ] **环境配置完整**：开发环境配置步骤详细完整
- [ ] **依赖关系明确**：技术依赖和模块依赖关系明确
- [ ] **实现路径清晰**：从需求到实现的路径清晰可行
- [ ] **任务可分配**：每个任务都可以直接分配给具体的开发人员
- [ ] **进度可跟踪**：任务进度和里程碑可以有效跟踪
- [ ] **质量可控制**：有明确的质量标准和检查机制

### 📊 质量评分标准
- **完整性** (25分)：内容完整，无空白占位符
- **准确性** (25分)：技术准确，符合最佳实践
- **一致性** (25分)：跨文档术语、数据、时间一致
- **可执行性** (25分)：可直接用于开发和项目管理

**评分等级**：
- **优秀** (90-100分)：可直接用于生产项目
- **良好** (80-89分)：稍作调整即可使用
- **合格** (70-79分)：需要一定程度的完善
- **不合格** (<70分)：需要重新生成或大幅修改

---

## 8. 附录

### 8.1 相关文档
- **PRD文档**：[链接到对应的PRD文档]
- **用户故事文档**：[链接到对应的用户故事文档]
- **CheeStack架构规范**：参考项目的fastapi-rule.md中的架构规范
- **FastAPI开发规则**：**严格遵循** `.augment/rules/fastapi-rule.md`
- **Flutter开发规则**：**严格遵循** `.augment/rules/flutter-rule.md`
- **API文档**：http://localhost:8000/docs（FastAPI自动生成）
- **工作流程文档**：`workflow/workflow.md`

### 8.2 技术规范文档

#### 后端技术规范
- **FastAPI规范文档**：`.augment/rules/fastapi-rule.md`
  - CheeStack项目架构规范
  - Tortoise ORM使用规范
  - API设计和响应格式规范
  - 异步编程最佳实践
  - 安全性和性能优化指南

#### 前端技术规范
- **Flutter规范文档**：`.augment/rules/flutter-rule.md`
  - GetX状态管理规范
  - MVVM架构模式规范
  - UI组件设计规范
  - 屏幕适配和主题规范
  - 测试规范和最佳实践

### 8.3 开发资源
- **开发环境配置**：参考development-guide.md
- **数据库脚本**：migrations/目录下的迁移脚本
- **测试数据**：tests/fixtures/目录下的测试数据
- **API调试工具**：http://localhost:8000/docs（Swagger UI）
- **Flutter开发工具**：`/Users/<USER>/dev/flutter/bin/flutter`

### 8.4 质量保证
- **代码审查清单**：参考各规范文档中的检查清单
- **测试覆盖率要求**：单元测试覆盖率 > 80%
- **性能基准**：API响应时间 < 500ms，页面加载时间 < 3s
- **安全扫描**：定期进行安全漏洞扫描

### 8.5 联系方式
- **开发负责人**：[姓名和联系方式]
- **技术支持**：[技术支持联系方式]
- **问题反馈**：[问题反馈渠道]
- **文档维护**：[文档维护负责人]
```

## 🔍 内容生成要求

### 模块化开发要求
1. **PRD对应性**：每个开发模块必须与PRD中的功能模块完全对应
2. **架构清晰性**：提供清晰的技术架构图和数据流设计图
3. **技术选型明确**：明确指定使用的技术栈和版本
4. **开发指导性**：提供足够的架构设计和实现思路指导

### 文档结构要求
1. **模块独立性**：每个模块的文档都是完整独立的
2. **架构完整性**：包含前端、后端、数据库、API的架构设计
3. **实施可行性**：架构设计合理，技术方案可行
4. **规范一致性**：严格遵循FastAPI和Flutter开发规则

### 质量标准
1. **架构合理性**：技术架构符合最佳实践，具备良好的扩展性
2. **文档一致性**：与PRD、技术规范等其他文档保持一致
3. **开发友好性**：开发人员可以基于架构设计进行具体实现
4. **维护便利性**：架构设计易于理解和维护

## ⚠️ 注意事项

### 🔥 强制性技术规范要求

#### 后端开发强制要求
1. **技术栈限制**：严格按照CheeStack架构（FastAPI + Tortoise ORM），**绝对禁止**使用其他技术替代
2. **规则遵循**：**必须严格遵循** `.augment/rules/fastapi-rule.md` 中的所有规范，无例外
3. **ORM限制**：**只能使用Tortoise ORM**，严禁使用SQLAlchemy或其他ORM
4. **目录结构**：严格遵循CheeStack项目的 `apps/[module_name]` 目录结构
5. **异步编程**：所有数据库操作和I/O操作必须使用async/await
6. **数据验证**：必须使用Pydantic v2进行数据验证和序列化
7. **响应格式**：必须使用core.responses中的统一响应格式

#### 前端开发强制要求
1. **状态管理限制**：**只能使用GetX**，严禁使用Provider、Bloc、Riverpod等其他状态管理
2. **规则遵循**：**必须严格遵循** `.augment/rules/flutter-rule.md` 中的所有规范，无例外
3. **状态更新方式**：使用GetBuilder配合update()更新，**严禁使用obs更新状态**
4. **屏幕适配**：**强制使用flutter_screenutil**进行屏幕适配
5. **主题颜色**：**必须使用** `Theme.of(Get.context!).colorScheme` 获取颜色，禁止硬编码颜色
6. **架构模式**：严格遵循MVVM + Repository架构模式
7. **测试方式**：使用flutter test，**严禁使用flutter run进行测试**

### 📋 文档生成要求

#### 内容完整性要求
1. **模板占位符**：所有 `[模块名称]`、`[实体]`、`[Entity]` 等占位符都需要替换为具体内容
2. **PRD同步**：开发文档必须与PRD中的功能需求保持同步
3. **架构重点**：重点关注架构设计和组件划分，提供必要的代码示例但避免过度详细的实现
4. **代码示例**：所有代码示例必须是完整可运行的，符合项目规范
5. **技术准确性**：所有技术方案必须经过验证，确保可行性

#### 质量标准要求
1. **完整性**：文档结构完整，无空白章节或占位符
2. **准确性**：技术方案准确可行，业务逻辑正确
3. **一致性**：术语使用一致，数据结构在各文档中保持一致
4. **可执行性**：开发人员可以基于文档直接进行开发实施

### 🚫 严格禁止事项

#### 技术选型禁止事项
- **后端禁止**：使用SQLAlchemy、Django ORM、Peewee等其他ORM
- **前端禁止**：使用Provider、Bloc、Riverpod等其他状态管理
- **前端禁止**：使用flutter run进行测试
- **前端禁止**：硬编码颜色值，不使用主题系统
- **前端禁止**：使用obs进行状态更新

#### 架构设计禁止事项
- **禁止偏离**：CheeStack项目架构规范
- **禁止忽略**：fastapi-rule.md和flutter-rule.md中的任何规范
- **禁止添加**：PRD和用户故事中未定义的功能
- **禁止使用**：未经验证的技术方案

### ✅ 开发检查清单

#### 开发前检查
- [ ] 已仔细阅读并理解fastapi-rule.md中的所有规范
- [ ] 已仔细阅读并理解flutter-rule.md中的所有规范
- [ ] 已确认技术选型符合项目要求
- [ ] 已确认架构设计符合CheeStack规范

#### 开发中检查
- [ ] 后端使用Tortoise ORM进行数据库操作
- [ ] 前端使用GetX进行状态管理
- [ ] 前端使用GetBuilder+update()进行状态更新
- [ ] 前端使用flutter_screenutil进行屏幕适配
- [ ] 前端使用Theme.of(Get.context!).colorScheme获取颜色

#### 开发后检查
- [ ] 所有测试通过，代码覆盖率>80%
- [ ] 代码符合项目规范和最佳实践
- [ ] API响应时间<500ms，页面加载时间<3s
- [ ] 无硬编码配置，使用环境变量管理配置
